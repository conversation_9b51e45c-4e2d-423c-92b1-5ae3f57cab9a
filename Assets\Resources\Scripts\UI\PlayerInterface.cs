using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System;

public class PlayerInterface : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IPointer<PERSON><PERSON><PERSON>and<PERSON>, IPointerDownHandler, IPointerUpHandler
{
    public GameObject ConfigHandlerOBJ; // Reference to the ConfigsHandler GameObject
    public GameObject Button; // Reference to the Button GameObject of the player interface
    public GameObject valueOBJ;

    public Image AttackCooldown; // Reference to the Image to show the attack cooldown

    public BattleCharacter pC; // Reference to the player character

    public bool didCrit = false;

    ConfigsHandler configsHandler; // Reference to the ConfigsHandler script

    Image healthBar;
    Image healthBarDelay;

    TextMeshProUGUI healthAmount;

    int playerIndex; // Index of the player

    // Click handling variables
    private float pointerDownTime;
    private bool isPointerDown = false;
    private bool longPressTriggered = false;
    private float lastClickTime = 0f;
    private int clickCount = 0;

    [Header("Click Settings")]
    public float longPressThreshold = 0.25f; // Time that the button needs to be pressed to trigger long press
    public float doubleClickThreshold = 0.3f; // Maximum time between clicks for double click

    private readonly float animationDuration = 0.5f;
    private Tween delayTween;

    Image gotHit;

    public Toggle slotNameToggle;

    public RectTransform target2DButton; // Drag the 2D button here
    public RectTransform panel3D; // The menu panel inside 3D canvas
    public Camera uiCamera; // The orthographic camera used by the 2D canvas
    public Camera worldCamera; // The perspective camera used by the 3D canvas
    public float zOffset = 1.0f; // Distance from camera
    public CameraEffect cameraEffect;

    GameObject focusPoint;

    private Vector3 originalLocalPosition;

    [SerializeField] private Material grayscaleMaterial; // Assign this in Inspector

    bool inCooldown = false;


    void Start()
    {
        playerIndex = int.Parse(name[^1..]); // Get the index of the player

        configsHandler = ConfigHandlerOBJ.GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        healthBar = transform.GetChild(2).GetChild(1).GetComponent<Image>();
        healthBarDelay = transform.GetChild(2).GetChild(0).GetComponent<Image>();
        healthAmount = transform.GetChild(2).GetChild(2).GetChild(0).GetComponent<TextMeshProUGUI>();

        gotHit = transform.GetChild(7).GetComponent<Image>();

        focusPoint = configsHandler.enemiesInterface[configsHandler.selectedEnemy];
        originalLocalPosition = transform.localPosition;
    }

    void Update()
    {
        Color currentColor = GetComponent<Image>().color; // gets the current color of the button
        UpdateStatusIcons(); // updates the status icons of the player

        pC = configsHandler.GetPlayerCharacter(playerIndex); // Get the player character


        if (GetComponent<Image>().sprite != Resources.Load<Sprite>("Sprites/UI/CharBuIdleT")) GetComponent<Image>().sprite = Resources.Load<Sprite>("Sprites/UI/CharBuIdleT");

        if (pC != null && !pC.IsDead) // checks if the player is alive or if it exists
        {

            if (transform.GetChild(5).gameObject.activeSelf) transform.GetChild(5).gameObject.SetActive(false); // hides the dead icon if the player isn't dead

            // changes the color of the button smoothly to white if the color is not white or the other two colors, which represents if the player is dead or if the player doesn't exist
            if (currentColor != new Color(1f, 0f, 0f, 0.9960784f)) GetComponent<Image>().color = Tools.MoveTowardsColor(currentColor, Color.black, Time.deltaTime * 2f);
            else GetComponent<Image>().color = Color.black;

            // Reduce the attack cooldown of the enemy. Speed of 0 takes 10 seconds,
            // speed of 200 takes 1 second, scaling linearly between those values
            if (AttackCooldown.fillAmount > 0.0001f)
            {
                inCooldown = true;
                float speed = Mathf.Max(0, pC.mods.GetSpeed());
                float duration = Mathf.Lerp(10f, 1f, Mathf.Clamp01(speed / 200f));
                // Defensive clamp to avoid extreme durations if speed ever goes out of expected range
                duration = Mathf.Clamp(duration, 0.1f, 20f);
                AttackCooldown.fillAmount -= Time.deltaTime / duration;
                SetCooldownVisual(inCooldown);
            }
            else
            {
                inCooldown = false;
                SetCooldownVisual(inCooldown);
            }



            // Updates the player interface values
            transform.GetChild(2).gameObject.SetActive(true); // health bar
            if (!slotNameToggle.isOn) transform.GetChild(3).gameObject.SetActive(true); // name
            transform.GetChild(6).gameObject.SetActive(pC.isStunned);


            healthBar.fillAmount = (pC.maxHP == 0) ? 1 : (float)pC.hP / pC.maxHP; // updates the health bar amount with the current health
            float targetFillAmount = (pC.maxHP == 0) ? 1 : (float)pC.hP / pC.maxHP;
            UpdateHealthBarDelay(targetFillAmount); // updates the health bar delay amount with the current health
            healthBar.color =
                Tools.InterpolateColor(
                    new(178f / 255f, 250f / 255f, 67f / 255f),
                    Color.red,
                    healthBar.fillAmount,
                    5f);

            healthAmount.text = pC.hP.ToString(); // updates the health text with the current health

            transform.GetChild(3).GetComponent<TextMeshProUGUI>().text = pC.name; // updates the name text with the current name
        }
        else
        {
            if (pC != null && pC.IsDead)
            {
                transform.GetChild(5).GetComponent<TextMeshProUGUI>().text = KeywordManager.GetWord("DEAD_ALERT"); // sets the dead text to the dead icon
                if (!transform.GetChild(5).gameObject.activeSelf) transform.GetChild(5).gameObject.SetActive(true); // shows the dead icon if the player is dead
                GetComponent<Image>().color = new Color(1f, 0f, 0f, 0.9960784f);
            }
            else
            {
                transform.GetChild(5).GetComponent<TextMeshProUGUI>().text = "EMPTY"; // sets the dead text to the dead icon
                GetComponent<Image>().color = Color.black;
            }

            // removes the cooldown
            AttackCooldown.fillAmount = 0;

            // hides the health bar and the name
            transform.GetChild(2).gameObject.SetActive(false);
            transform.GetChild(3).gameObject.SetActive(false);
            transform.GetChild(5).gameObject.SetActive(true);
            transform.GetChild(6).gameObject.SetActive(false);
        }

        // Handle long press detection in Update
        if (isPointerDown && !longPressTriggered)
        {
            if (Time.time - pointerDownTime >= longPressThreshold)
            {
                longPressTriggered = true;
                OnLongPress();
            }
        }

        // changes the sprite of the button's border, its not perfect but is better than have the sprite over it when the player is hitted
        // there is way to much orange in the effects, letting the sprite over it makes the hitted effect look orange instead of red
        if (currentColor == new Color(1f, 0f, 0f, 0.9960784f) || currentColor == Color.gray || currentColor == new Color(0, 0, 0, 1))
            transform.GetChild(0).GetComponent<Image>().sprite = Resources.Load<Sprite>("Sprites/UI/CharBuIdleT1");
        else
            transform.GetChild(0).GetComponent<Image>().sprite = Resources.Load<Sprite>("Sprites/UI/CharBuF");

        // shows the selected highlight if the player is selected
        if (pC != null && !pC.IsDead)
        {
            transform.GetChild(0).GetComponent<Image>().enabled = configsHandler.GetPlayerSelected(playerIndex);
            RaiseSelectedPlayer(configsHandler.GetPlayerSelected(playerIndex));
        }
        else transform.GetChild(0).GetComponent<Image>().enabled = false;

        // if the previous is player is null or dead, select the player
        if (!configsHandler.IsAnyPlayerSelected() && (configsHandler.GetPlayerCharacter(Math.Max(0, playerIndex - 1)) == null || configsHandler.GetPlayerCharacter(Math.Max(0, playerIndex - 1)).IsDead))
            PlayerGotSelected();

        // Hides the player sprite if the player is dead or doesn't exist
        transform.GetChild(0).GetChild(0).gameObject.SetActive(pC != null && !pC.IsDead);

    }



    void UpdateHealthBarDelay(float targetFillAmount)
    {
        // Kill existing tween to prevent overlapping
        delayTween?.Kill();

        // Start smooth animation with ease-in
        delayTween = healthBarDelay.DOFillAmount(targetFillAmount, animationDuration);
    }

    IEnumerator ShowSkillValues() // Coroutine to show the values of the player
    {
        yield return null;
        yield return null;

        valueOBJ.SetActive(true); // Set the EnemyValues GameObject to active
        valueOBJ.transform.SetAsLastSibling();
        float x = 0;
        float y = 2;

        valueOBJ.transform.position = new Vector3(x, y, transform.position.z) + new Vector3(0f, 0f, -1.25f); // Set the position of the EnemyValues GameObject to the enemy position

        valueOBJ.GetComponent<EnemyValues>().UpdateCharacter(configsHandler.GetPlayerCharacter(playerIndex)); // Makes the EnemyValues GameObject show the values of the enemy
    }

    bool PlayerGotSelected()
    {
        bool canSelectPlayer;

        canSelectPlayer = pC != null && !pC.IsDead && !pC.isStunned && AttackCooldown.fillAmount <= 0.0001f && configsHandler.playerTurn;

        if (canSelectPlayer) configsHandler.SetPlayerSelected(playerIndex); // selects the player
        return canSelectPlayer;
    }

    #region Pointer Event Handlers

    public void OnPointerDown(PointerEventData eventData)
    {
        if (eventData.button != PointerEventData.InputButton.Left)
            return;

        isPointerDown = true;
        pointerDownTime = Time.time;
        longPressTriggered = false;
    }

    public void OnPointerUp(PointerEventData eventData)
    {
        if (eventData.button != PointerEventData.InputButton.Left)
            return;

        isPointerDown = false;

        // If long press was triggered, don't process click
        if (longPressTriggered)
        {
            longPressTriggered = false;
            return;
        }

        // Check if it was a quick release (not a long press)
        float pressDuration = Time.time - pointerDownTime;
        if (pressDuration < longPressThreshold)
        {
            HandleClick();
        }
    }

    public void OnPointerClick(PointerEventData eventData)
    {
        // This is called by Unity's event system, but we handle clicks in OnPointerUp
        // to have better control over timing with long press detection
    }

    #endregion

    #region Click Handling Methods

    private void HandleClick()
    {
        float currentTime = Time.time;

        // Check for double click
        if (currentTime - lastClickTime <= doubleClickThreshold)
        {
            clickCount++;
            if (clickCount >= 2)
            {
                // Double click detected - cancel any pending single click and execute double click
                StopAllCoroutines(); // Stop any pending single click
                OnDoubleClick();
                clickCount = 0;
                lastClickTime = 0f;
                return;
            }
        }
        else
        {
            clickCount = 1;
        }

        lastClickTime = currentTime;

        // Execute single click immediately
        OnSingleClick();

        // Start coroutine to potentially override with double click
        StartCoroutine(HandleDoubleClickWindow());
    }

    private IEnumerator HandleDoubleClickWindow()
    {
        yield return new WaitForSeconds(doubleClickThreshold);

        // Reset click count after double click window expires
        if (clickCount == 1)
        {
            clickCount = 0;
        }
    }

    private void OnSingleClick()
    {
        if (!configsHandler.ActionMenu.activeSelf)
        {
            // play the select sound effect
            bool canSelectPlayer;
            canSelectPlayer = pC != null && !pC.IsDead && !pC.isStunned && AttackCooldown.fillAmount <= 0.0001f && configsHandler.playerTurn;
            ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/" + (canSelectPlayer ? "MenuSound" : "RejectedSelect")));
            PlayerGotSelected();
            PlaySelectedAnimation();
        }
    }

    private void OnDoubleClick()
    {
        if (!configsHandler.ActionMenu.activeSelf)
        {
            // For double click, we want to show skill values regardless of selection state
            // The first click already selected the player, so now we show values
            if (!valueOBJ.activeSelf && pC != null && !pC.IsDead)
            {
                StartCoroutine(ShowSkillValues());
            }
        }
    }

    private void OnLongPress()
    {
        if (!configsHandler.ActionMenu.activeSelf)
        {
            if (PlayerGotSelected())
            {
                // play the select sound effect
                ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));



                configsHandler.ActionMenu.SetActive(true);
                configsHandler.ActionMenu.GetComponent<ActionMenu>().active = true;
                configsHandler.ActionMenu.transform.position = transform.position - new Vector3(0, 1.6f, 0.1f);
                //cameraEffect.MoveToMenuView(focusPoint.transform.position, playerIndex, configsHandler.ActionMenu);
            }
        }
    }

    private void PlaySelectedAnimation()
    {
        Transform target = transform.GetChild(0); // The card visual

        // Kill any ongoing scale tweens on this transform to avoid stacking
        target.DOKill();

        // Reset scale to default
        target.localScale = Vector3.one;

        // Scale up slightly (pop), then back down with bounce
        target.DOScale(1.1f, 0.1f)
            .SetEase(Ease.OutQuad)
            .OnComplete(() =>
            {
                target.DOScale(1f, 0.2f)
                    .SetEase(Ease.OutBack); // Back gives a nice bounce effect
            });
    }

    private void RaiseSelectedPlayer(bool isSelected)
    {
        if (isSelected)
        {
            // Raise the card slightly
            transform.DOLocalMoveY(originalLocalPosition.y + 0.3f, 0.2f)
                    .SetEase(Ease.OutCubic);
        }
        else
        {
            // Return to original position
            transform.DOLocalMoveY(originalLocalPosition.y, 0.2f)
                    .SetEase(Ease.OutCubic);
        }
    }

    public void SetCooldownVisual(bool isInCooldown)
    {
        var graphics = GetComponentsInChildren<Graphic>(includeInactive: true);

        foreach (var graphic in graphics)
        {
            // Skip the "AttackRecharge" object
            if (graphic.gameObject.name == "AttackRecharge")
                continue;

            graphic.material = isInCooldown ? grayscaleMaterial : null;
        }

    }


    public void Show3DMenu()
    {
        // Convert the 2D button's center to screen position
        Vector3 screenPos = RectTransformUtility.WorldToScreenPoint(uiCamera, target2DButton.position);

        // Convert screen position to world space in the 3D camera's view
        Vector3 worldPos = worldCamera.ScreenToWorldPoint(new Vector3(screenPos.x, screenPos.y - 370f, zOffset));

        // Move the 3D menu to that position
        panel3D.position = worldPos;

        // Make sure it's facing the right direction
        //panel3D.rotation = Quaternion.LookRotation(panel3D.position - worldCamera.transform.position);

        // Optionally: apply a tilt
        //panel3D.Rotate(0, 15f, 0); // Tilt for perspective effect

        // Enable the panel
        panel3D.gameObject.SetActive(true);
        //cameraEffect.MoveToMenuView(panel3D.position);
    }

    #endregion

    // changes the color of the button to show that the player was hitted,
    // the WaitForSeconds is to make the effect being aplicated after
    // the damage star to the player interface
    public IEnumerator GotHitted()
    {
        yield return new WaitForSeconds(0.1f);
        AttackCooldown.fillAmount = 0;
        gotHit.gameObject.SetActive(true);
        yield return new WaitForSeconds(0.2f);
        gotHit.gameObject.SetActive(false);
    }

    public void AddStatusIcon(Sprite icon, string name) // adds a status icon to the player interface
    {
        BattleCharacter temp = configsHandler.GetCharacterByID(pC.id); // gets the player character

        temp.statusIcons.Add(name, new StatusIcon(icon, name, gameObject)); // adds the status icon to the player character

        configsHandler.SetCharacter(temp, configsHandler.GetCharacter(pC)); // updates the player character
    }

    public void RemoveStatusIcon(string name) // removes a status icon from the player interface
    {
        BattleCharacter temp = configsHandler.GetCharacterByID(pC.id); // gets the player character
        temp.statusIcons[name].icon.GetComponent<SkillIconUpdate>().remove = true; // sets the remove bool to true to remove the icon
        configsHandler.SetCharacter(temp, configsHandler.GetCharacter(pC)); // updates the player character
    }

    public void UpdateStatusIcons() // updates the status icons of the player interface
    {
        if (pC == null) return; // returns if the player character is null
        int counter = 0;
        foreach (KeyValuePair<string, StatusIcon> icon in pC.statusIcons) // loops through all the status icons of the player character and updates their position
        {
            icon.Value.icon.transform.position = transform.position + new Vector3((pC.statusIcons.Count - 1) * -0.125f + counter * 0.25f, 0.6f, 0f);
            counter++;
        }
    }
}

public class StatusIcon // Class to handle the status icons of the player interface
{
    public GameObject icon; // Reference to the icon GameObject

    public StatusIcon(Sprite icon, string name, GameObject parent) // Constructor to create the status icon
    {
        GameObject iconPrefab = Resources.Load<GameObject>("Prefabs/SkillIcon"); // Load the icon prefab

        this.icon = UnityEngine.Object.Instantiate(iconPrefab, parent.transform); // Instantiate the icon prefab

        this.icon.GetComponent<Image>().sprite = icon; // Set the icon sprite
        this.icon.name = name; // Set the icon name
    }
}