
using System.Collections;
using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class InfoBox : MonoBehaviour
{
    public static InfoBox Instance { get; private set; }
    public Canvas canvas;
    private RectTransform canvasRect;
    public Camera uiCamera;
    private RectTransform uiCameraRect;
    public GameObject info;
    GameObject slot1, slot2;
    GameObject S1title, S2title;
    GameObject type;
    Image S1titleIcon, S2titleIcon, typeIcon;
    TextMeshProUGUI S1titleText, typeText, S2titleText;
    TextMeshProUGUI S1description, S2description;

    private bool isAnimating = false;

    //Animation
    Vector3 initialScale;
    // Start is called once before the first execution of Update after the MonoBehaviour is created

    void Awake()
    {
        // Singleton setup
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        Instance = this;
    }

    void Start()
    {
        Instance = this;

        canvasRect = canvas.GetComponent<RectTransform>();
        uiCameraRect = uiCamera.GetComponent<RectTransform>();

        // Info setup
        initialScale = info.transform.localScale;
        info.transform.localScale = Vector3.zero;
        info.SetActive(false);

        type = info.transform.GetChild(0).gameObject;
        slot1 = info.transform.GetChild(1).gameObject;
        slot2 = info.transform.GetChild(2).gameObject;

        typeIcon = type.transform.GetChild(0).GetComponent<Image>();
        typeText = type.transform.GetChild(1).GetComponent<TextMeshProUGUI>();

        S1title = slot1.transform.GetChild(0).gameObject;
        S2title = slot2.transform.GetChild(0).gameObject;

        S1titleIcon = S1title.transform.GetChild(0).GetComponent<Image>();
        S1titleText = S1title.transform.GetChild(1).GetComponent<TextMeshProUGUI>();
        S1description = slot1.transform.GetChild(1).GetComponent<TextMeshProUGUI>();

        S2titleIcon = S2title.transform.GetChild(0).GetComponent<Image>();
        S2titleText = S2title.transform.GetChild(1).GetComponent<TextMeshProUGUI>();
        S2description = slot2.transform.GetChild(1).GetComponent<TextMeshProUGUI>();


    }

    // Update is called once per frame
    void Update()
    {
        if (Input.touchCount > 0 && Input.GetTouch(0).phase == TouchPhase.Began)
        {
            if (info.activeSelf) HideInfo();
        }
    }

    public void DisplayAilmentOrSkill(Transform transform, bool isAilment, string ailment = null, string defense = null)
    {
        if (info.activeSelf)
        {
            HideInfo();
            return;
        }

        if (isAilment)
        {
            string spritePath = defense switch
            {
                "Fraco" => "Sprites/UI/Fraco",
                "Resiste" => "Sprites/UI/Resiste",
                "Imune" => "Sprites/UI/Nulo",
                "Repele" => "Sprites/UI/Repelir",
                "Absorver" => "Sprites/UI/Absorver",
                "Nulo" => "Sprites/UI/Nulo",
                "Resistência" => "Sprites/UI/Resiste",
                "Fraqueza" => "Sprites/UI/Fraco",
                "Repelir" => "Sprites/UI/Repelir",
                "Normal" => "Sprites/UI/Normal",
                _ => null
            };

            type.SetActive(false);
            slot1.SetActive(true);
            S1title.SetActive(true);
            S1titleIcon.gameObject.SetActive(true);
            S1titleIcon.sprite = Resources.Load<Sprite>($"Sprites/UI/{ailment}");
            S1titleText.text = ailment;
            S1description.text = GeneralInfo.GetAilmentDescriptionByName(ailment);

            slot2.SetActive(true);
            S2titleIcon.sprite = Resources.Load<Sprite>(spritePath);
            S2titleText.text = defense;
            string defenseId = GeneralInfo.GetElementalDefensesIdByName(defense);
            S2description.text = GeneralInfo.GetElementalDefensesDescription(defenseId);
        }
        else
        {
            string spritePath = defense switch
            {
                "Fraco" => "Sprites/UI/Fraco",
                "Resiste" => "Sprites/UI/Resiste",
                "Imune" => "Sprites/UI/Nulo",
                "Repele" => "Sprites/UI/Repelir",
                "Absorver" => "Sprites/UI/Absorver",
                "Nulo" => "Sprites/UI/Nulo",
                "Resistência" => "Sprites/UI/Resiste",
                "Fraqueza" => "Sprites/UI/Fraco",
                "Repelir" => "Sprites/UI/Repelir",
                "Normal" => "Sprites/UI/Normal",
                _ => null
            };

            type.SetActive(false);
            slot1.SetActive(false);

            slot2.SetActive(true);
            S2title.SetActive(true);
            S2titleIcon.gameObject.SetActive(true);
            S2titleIcon.sprite = Resources.Load<Sprite>(spritePath);
            S2titleText.text = defense;
            string defenseId = GeneralInfo.GetElementalDefensesIdByName(defense);
            S2description.text = GeneralInfo.GetElementalDefensesDescription(defenseId);
        }

        // Reset scale
        info.transform.localScale = Vector3.zero;

        // Force layout rebuild
        StartCoroutine(UpdateLayoutNextFrame());

        //Get Position of the cursor
        Vector2 cursPos = uiCamera.ScreenToWorldPoint(Input.GetTouch(0).position);
        info.transform.position = cursPos;

        //Change pivot to bottom center
        //info.GetComponent<RectTransform>().pivot = new Vector2(0.5f, 0f);
        DeterminePivotAndPosition();

        info.SetActive(true);
        gameObject.transform.SetAsLastSibling();

    }


    public void DisplayClassORArchetype(Transform transform, bool isClass, string name, string description)
    {
        if (info.activeSelf)
        {
            HideInfo();
            return;
        }

        if (isClass)
        {
            type.SetActive(true);
            slot1.SetActive(true);

            typeIcon.gameObject.SetActive(true);
            typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/Class");
            typeText.text = KeywordManager.GetWord("TEXT_CLASS");

            S1titleIcon.gameObject.SetActive(false);
            S1titleText.text = name;
            S1description.text = description;

            slot2.SetActive(false);
        }
        else
        {
            type.SetActive(true);
            slot1.SetActive(true);

            typeIcon.gameObject.SetActive(true);
            typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/Archetype");
            typeText.text = KeywordManager.GetWord("TEXT_ARCHETYPE");

            S1titleIcon.gameObject.SetActive(false);
            S1titleText.text = name;
            S1description.text = description;

            slot2.SetActive(false);
        }

        // Reset scale
        info.transform.localScale = Vector3.zero;

        // Force layout rebuild
        StartCoroutine(UpdateLayoutNextFrame());

        //Change pivot to bottom center
        DeterminePivotAndPosition();

        //Get Position of the cursor
        Vector2 cursPos = uiCamera.ScreenToWorldPoint(Input.GetTouch(0).position);
        info.transform.position = cursPos;

        info.SetActive(true);
        gameObject.transform.SetAsLastSibling();
    }

    public void DisplayModifier(Transform targetTransform, string name, string description)
    {
        if (info.activeSelf)
        {
            HideInfo();
            return;
        }

        type.SetActive(true);
        typeIcon.gameObject.SetActive(false);
        typeText.text = KeywordManager.GetWord("COLLECTIBLE_MODIFIERS");
        slot1.SetActive(true);

        S1title.SetActive(true);
        S1titleIcon.gameObject.SetActive(false);
        S1titleText.text = name;
        S1description.text = description;

        slot2.SetActive(false);

        // Reset scale
        info.transform.localScale = Vector3.zero;

        // Force layout rebuild
        StartCoroutine(UpdateLayoutNextFrame());

        //Change pivot to bottom center
        info.GetComponent<RectTransform>().pivot = new Vector2(0.5f, 0f);

        //Get Position of the cursor
        Vector2 cursPos = uiCamera.ScreenToWorldPoint(Input.GetTouch(0).position);
        info.transform.position = cursPos;

        info.SetActive(true);
        gameObject.transform.SetAsLastSibling();
    }

    public void DisplayAgeOrHeight(Transform transform, bool isAge)
    {
        if (info.activeSelf)
        {
            HideInfo();
            return;
        }

        if (isAge)
        {
            type.SetActive(true);
            typeIcon.gameObject.SetActive(true);
            typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/Age");
            typeText.text = KeywordManager.GetWord("TITLE_AGE");

            slot1.gameObject.SetActive(true);
            S1title.gameObject.SetActive(false);
            S1description.text = KeywordManager.GetWord("INFO_AGE");

            slot2.gameObject.SetActive(false);
        }
        else
        {
            type.SetActive(true);
            typeIcon.gameObject.SetActive(true);
            typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/Height");
            typeText.text = KeywordManager.GetWord("TITLE_HEIGHT");

            slot1.gameObject.SetActive(true);
            S1title.gameObject.SetActive(false);
            S1description.text = KeywordManager.GetWord("INFO_HEIGHT");

            slot2.gameObject.SetActive(false);
        }

        // Reset scale
        info.transform.localScale = Vector3.zero;

        // Force layout rebuild
        StartCoroutine(UpdateLayoutNextFrame());

        //Change pivot to bottom center
        info.GetComponent<RectTransform>().pivot = new Vector2(0.5f, 0f);

        //Get Position of the cursor
        Vector2 cursPos = uiCamera.ScreenToWorldPoint(Input.GetTouch(0).position);
        info.transform.position = cursPos;

        info.SetActive(true);
        gameObject.transform.SetAsLastSibling();
    }

    public void DisplayGender(Transform transform, string gender)
    {
        if (info.activeSelf)
        {
            HideInfo();
            return;
        }

        type.SetActive(true);

        switch (gender)
        {
            case "1":
                typeIcon.gameObject.SetActive(true);
                typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/female-sign");
                typeText.gameObject.SetActive(true);
                typeText.text = KeywordManager.GetWord("SEX_FEMALE");
                break;
            case "2":
                typeIcon.gameObject.SetActive(true);
                typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/male-sign");
                typeText.gameObject.SetActive(true);
                typeText.text = KeywordManager.GetWord("SEX_MALE");
                break;
            default:
                typeIcon.gameObject.SetActive(true);
                typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/Undefined");
                typeText.gameObject.SetActive(true);
                typeText.text = KeywordManager.GetWord("SEX_UNDEFINED");
                break;
        }

        slot1.gameObject.SetActive(true);
        S1title.gameObject.SetActive(true);
        S1titleIcon.gameObject.SetActive(false);
        S1titleText.text = KeywordManager.GetWord("TITLE_SEX");
        S1description.text = KeywordManager.GetWord("INFO_SEX");

        slot2.gameObject.SetActive(false);

        // Reset scale
        info.transform.localScale = Vector3.zero;

        // Force layout rebuild
        StartCoroutine(UpdateLayoutNextFrame());

        //Change pivot to bottom center
        info.GetComponent<RectTransform>().pivot = new Vector2(0.5f, 0f);

        //Get Position of the cursor
        Vector2 cursPos = uiCamera.ScreenToWorldPoint(Input.GetTouch(0).position);
        info.transform.position = cursPos;

        info.SetActive(true);
        gameObject.transform.SetAsLastSibling();
    }



    public void HideInfo()
    {
        if (isAnimating) return;
        isAnimating = true;

        // Hide animation
        info.transform.DOScale(Vector3.zero, 0.2f)
            .SetEase(Ease.InBack)
            .OnComplete(() =>
            {
                info.SetActive(false);
                isAnimating = false;
            });
    }

    private IEnumerator UpdateLayoutNextFrame()
    {
        yield return null; // wait 1 frame

        LayoutRebuilder.ForceRebuildLayoutImmediate(info.GetComponent<RectTransform>());

        info.transform.DOScale(initialScale, 0.2f).SetEase(Ease.OutBack);
    }

    //Determine pivot and position based on the cursor position and screen space
    private void DeterminePivotAndPosition()
    {
        // Get the InfoBox RectTransform
        RectTransform infoRect = info.GetComponent<RectTransform>();

        // Get cursor position in world space
        Vector2 cursPos = uiCamera.ScreenToWorldPoint(Input.GetTouch(0).position);

        // Force layout rebuild to get accurate size
        LayoutRebuilder.ForceRebuildLayoutImmediate(infoRect);

        // Get InfoBox size
        Vector2 infoSize = infoRect.sizeDelta;

        // Get screen dimensions in world space
        Vector3 screenBottomLeft = uiCamera.ScreenToWorldPoint(Vector3.zero);
        Vector3 screenTopRight = uiCamera.ScreenToWorldPoint(new Vector3(Screen.width, Screen.height, 0));

        float screenTop = screenTopRight.y;
        float screenLeft = screenBottomLeft.x;
        float screenRight = screenTopRight.x;

        // Calculate proposed position with bottom-center pivot (InfoBox appears above cursor)
        Vector2 proposedPosition = cursPos;

        // Calculate InfoBox bounds with bottom-center pivot
        float infoTop = proposedPosition.y + infoSize.y;
        float infoLeft = proposedPosition.x - (infoSize.x * 0.5f);
        float infoRight = proposedPosition.x + (infoSize.x * 0.5f);

        // Check if InfoBox would extend beyond screen boundaries
        bool fitsAbove = infoTop <= screenTop;
        bool fitsHorizontally = infoLeft >= screenLeft && infoRight <= screenRight;

        Vector2 finalPivot;
        Vector2 finalPosition;

        if (fitsAbove)
        {
            // Use bottom-center pivot (InfoBox appears above cursor)
            finalPivot = new Vector2(0.5f, 0f);
            finalPosition = proposedPosition;
        }
        else
        {
            // Use top-center pivot (InfoBox appears below cursor)
            finalPivot = new Vector2(0.5f, 1f);
            finalPosition = cursPos;
        }

        // Ensure horizontal boundaries are respected regardless of pivot choice
        if (!fitsHorizontally)
        {
            // Clamp the X position to keep InfoBox within screen bounds
            float halfWidth = infoSize.x * 0.5f;
            finalPosition.x = Mathf.Clamp(finalPosition.x, screenLeft + halfWidth, screenRight - halfWidth);
        }

        // Apply the calculated pivot and position
        infoRect.pivot = finalPivot;
        info.transform.position = finalPosition;
    }

}
