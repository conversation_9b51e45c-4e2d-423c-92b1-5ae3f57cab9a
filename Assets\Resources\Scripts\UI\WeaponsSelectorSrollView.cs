using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

//TODO: Implement virtualization and pooling
public class WeaponsSelectorSrollView : MonoBehaviour
{
    public ConfigsHandler configsHandler;

    //Pooling and Virtualization
    [SerializeField] private ScrollRect scrollRect;
    private float itemWidth = 330f; // width of one weapon prefab
    private int buffer = 2; // extra items off-screen to avoid popping
    private float itemSpacing = 20f;

    private int visibleItemCount;
    private int totalItemCount;
    private List<Weapons> weapons = new();
    private List<Weapons> filteredWeapons = new();
    private List<Weapons> noBlueprints = new();
    private Queue<GameObject> pooledItems = new(); // Object pool
    private Dictionary<int, GameObject> activeItems = new(); // index -> GameObject


    public BattleCharacter selectedCharacter;
    private BattleCharacter previousCharacter;
    Vector3 origin; // The origin of the UI
    public Button closeButton;

    public Transform content;
    public GameObject weaponPrefab;

    [SerializeField] private Material grayscaleMaterial; // Assign this in Inspector
    List<GameObject> weaponPrefabs = new();

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        gameObject.SetActive(false);
        origin = new(0f, -3f * transform.localScale.y, transform.position.z); // Set the origin
        closeButton.onClick.AddListener(() =>
        {
            gameObject.SetActive(false);
        });

        LoadWeapons();
    }

    // Update is called once per frame
    void Update()
    {
        selectedCharacter = configsHandler.GetSelectedPlayerCharacter();

        if (selectedCharacter != previousCharacter)
        {
            previousCharacter = selectedCharacter;
            RefreshScrollView(); // Reload filtered list
        }

        UpdateVisibleItems();
        //UpdateVisuals();

        // If the weapon is being used, turn on grayscale
        foreach (var weapon in activeItems.Values)
        {

            var graphics = weapon.GetComponentsInChildren<Graphic>(includeInactive: true);
            foreach (var graphic in graphics)
            {
                if (graphic.gameObject.name == "removeBtn") continue;

                graphic.material = weapon.GetComponent<WeaponPrefab>().isBeingUsed ? grayscaleMaterial : null;
            }

        }
    }



    void OnEnable()
    {
        transform.position = origin; // Set the position
    }

    void LoadWeapons()
    {
        GetWeaponsAfterRequirements(); // populate filteredWeapons
        weapons = filteredWeapons;     // use filtered list
        totalItemCount = weapons.Count;

        // Set content width
        //Account for spacing between items
        float contentWidth = (itemWidth + itemSpacing) * totalItemCount + itemSpacing;
        RectTransform contentRect = content.GetComponent<RectTransform>();
        contentRect.sizeDelta = new Vector2(contentWidth, contentRect.sizeDelta.y);

        // Determine how many items fit in view
        float viewportWidth = scrollRect.viewport.rect.width;
        visibleItemCount = Mathf.CeilToInt(viewportWidth / itemWidth) + buffer;

        // Pre-instantiate pooled items, up to the number of visible items
        int poolTargetSize = Mathf.Min(visibleItemCount, weapons.Count);
        while (pooledItems.Count < poolTargetSize)
        {
            var go = Instantiate(weaponPrefab, content);
            go.SetActive(false);
            pooledItems.Enqueue(go);
        }

        UpdateVisibleItems(); // Initial update

    }

    void UpdateVisibleItems()
    {
        float contentX = -content.GetComponent<RectTransform>().anchoredPosition.x; // how far we've scrolled (positive value)
        float viewportWidth = scrollRect.viewport.rect.width;

        float itemTotalWidth = itemWidth + itemSpacing;
        float visibilityBuffer = 50f;

        // Calculate the visible range with a buffer to keep partially visible items
        float startX = contentX - visibilityBuffer;
        float endX = contentX + viewportWidth + visibilityBuffer;

        int firstVisibleIndex = Mathf.FloorToInt(startX / itemTotalWidth);
        int lastVisibleIndex = Mathf.CeilToInt(endX / itemTotalWidth);

        firstVisibleIndex = Mathf.Max(0, firstVisibleIndex);
        lastVisibleIndex = Mathf.Min(totalItemCount - 1, lastVisibleIndex);

        // Track which indices are supposed to be visible
        var indicesToKeep = new HashSet<int>();
        for (int i = firstVisibleIndex; i <= lastVisibleIndex; i++)
        {
            indicesToKeep.Add(i);

            if (!activeItems.ContainsKey(i))
            {
                GameObject item = GetPooledItem();
                SetupItem(item, i);
                activeItems[i] = item;
            }
        }

        // Recycle items no longer in view
        List<int> toRemove = new List<int>();
        foreach (var kvp in activeItems)
        {
            if (!indicesToKeep.Contains(kvp.Key))
            {
                kvp.Value.SetActive(false);
                kvp.Value.GetComponent<WeaponPrefab>().CleanupForPooling();
                pooledItems.Enqueue(kvp.Value);
                toRemove.Add(kvp.Key);
            }
        }

        foreach (int index in toRemove)
            activeItems.Remove(index);
    }

    GameObject GetPooledItem()
    {
        if (pooledItems.Count > 0)
            return pooledItems.Dequeue();

        // Only create more if really necessary (scrolling fast or small pool)
        var go = Instantiate(weaponPrefab, content);
        go.SetActive(false);
        return go;
    }

    void SetupItem(GameObject item, int index)
    {
        item.SetActive(true);
        var weapon = weapons[index];
        var rect = item.GetComponent<RectTransform>();
        //Add left padding of spacing
        rect.anchoredPosition = new Vector2(index * (itemWidth + itemSpacing) + itemSpacing, 0f);

        var wp = item.GetComponent<WeaponPrefab>();
        wp.weapon = weapon;
        wp.RefreshUI(); // Updates the UI from the weapon data
    }

    void RefreshScrollView()
    {
        // Clear current items
        foreach (var item in activeItems.Values)
        {
            item.SetActive(false);
            item.GetComponent<WeaponPrefab>().CleanupForPooling();
            pooledItems.Enqueue(item);
        }
        activeItems.Clear();

        LoadWeapons(); // Load filtered weapons and update scroll view

        // Reset scroll position
        //scrollRect.horizontalNormalizedPosition = 0f;
    }

    void UpdateVisuals()
    {
        selectedCharacter = configsHandler.GetSelectedPlayerCharacter();

        int visibleWeaponsCount = 0;
        int totalWeapons = configsHandler.weapons.Count;

        foreach (var weapon in configsHandler.weapons)
        {
            if (selectedCharacter != null && selectedCharacter.mods != null)
            {
                if (selectedCharacter.mods.GetKnowledge() >= weapon.qiMin ||
                    selectedCharacter.mods.GetLuck() >= weapon.luckMin)
                {
                    visibleWeaponsCount++;
                }
            }
        }

        Debug.Log($"Visible Weapons: {visibleWeaponsCount}/{totalWeapons} " +
                $"(Character Knowledge: {selectedCharacter?.mods?.GetKnowledge()}, " +
                $"Luck: {selectedCharacter?.mods?.GetLuck()})");

    }

    // Filters weapons based on selected character knowledge and luck
    public void GetWeaponsAfterRequirements()
    {
        if (selectedCharacter == null) return;
        Debug.Log("Selected Character: " + selectedCharacter.name);
        selectedCharacter = configsHandler.GetSelectedPlayerCharacter();

        if (selectedCharacter == null || selectedCharacter.mods == null) return;

        FilterOutBlueprints();

        filteredWeapons = noBlueprints.FindAll(w =>
            selectedCharacter.mods.GetKnowledge() >= w.qiMin &&
            selectedCharacter.mods.GetLuck() >= w.luckMin
        );
    }

    public void FilterOutBlueprints()
    {
        foreach (var weapon in configsHandler.weapons)
        {
            if (!weapon.name.Contains("Blueprint"))
            {
                noBlueprints.Add(weapon);
            }
        }
    }



}
