using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class WeaponPrefab : MonoBehaviour
{
    ConfigsHandler configsHandler;
    WeaponsSelectorSrollView weaponSelector;
    Canvas canvas;
    public Weapons weapon;
    Image background;
    Image outline;
    Image weaponImage;
    TextMeshProUGUI atkw;
    TextMeshProUGUI lvl;
    Button removeButton;
    GameObject starContainer;
    GameObject star1, star2, star3, star4, star5;
    GameObject curse;
    TextMeshProUGUI shots;
    Sprite[] weaponSprites;

    GameObject weaponIcon;

    public bool isBeingUsed = false;


    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();
        weaponSelector = GameObject.Find("WeaponsSelectorSrollView").GetComponent<WeaponsSelectorSrollView>();
        canvas = GameObject.Find("Canvas").GetComponent<Canvas>();

        weaponSprites = new Sprite[]
        {
            Resources.Load<Sprite>("Sprites/Weapons/W1"),
            Resources.Load<Sprite>("Sprites/Weapons/W2"),
            Resources.Load<Sprite>("Sprites/Weapons/W3"),
            Resources.Load<Sprite>("Sprites/Weapons/W4"),
            Resources.Load<Sprite>("Sprites/Weapons/W5")
        };

        background = transform.GetChild(0).GetComponent<Image>(); // Set the background image
        outline = transform.GetChild(1).GetComponent<Image>(); // Set the outline image
        weaponImage = transform.GetChild(2).GetComponent<Image>(); // Set the weapon image
        atkw = transform.GetChild(3).GetChild(1).GetComponent<TextMeshProUGUI>(); // Set the weapon attack
        lvl = transform.GetChild(4).GetComponent<TextMeshProUGUI>(); // Set the weapon level
        curse = transform.GetChild(6).gameObject; // Set the curse image
        shots = transform.GetChild(7).GetChild(0).GetChild(0).GetComponent<TextMeshProUGUI>(); // Set the weapon shots
        removeButton = transform.GetChild(11).GetComponent<Button>(); // Set the remove button

        starContainer = transform.GetChild(5).gameObject; // Set the star container, number of stars related to the weapon rarity
        star1 = starContainer.transform.GetChild(0).gameObject;
        star2 = starContainer.transform.GetChild(1).gameObject;
        star3 = starContainer.transform.GetChild(2).gameObject;
        star4 = starContainer.transform.GetChild(3).gameObject;
        star5 = starContainer.transform.GetChild(4).gameObject;
        starContainer.SetActive(false);

        atkw.text = weapon.atkw.ToString();
        // If weapon has tg30 tag, show curse image
        if (weapon.weaponTags.Any(x => x.id == "tg30"))
        {
            curse.SetActive(true);
        } else
        {
            curse.SetActive(false);
        }
        shots.text = weapon.shots.ToString();
        lvl.text = "Lv. " + weapon.wlBase.ToString();
        //Each gets a random sprite from the weaponSprites list
        //weaponImage.sprite = weaponSprites[Random.Range(0, weaponSprites.Length)];

        gameObject.GetComponent<Button>().onClick.AddListener(() => // Add the listener to the button to equip the weapon
        {
            EquipWeapon();
            //LevelUpWeapon();
        });

        removeButton.onClick.AddListener(() => // Add the listener to the button to remove the weapon
        {
            RemoveWeapon();
        });



    }

    // Update is called once per frame
    void Update()
    {
        if (weapon == weaponSelector.selectedCharacter.weaponHeld)
        {
            removeButton.gameObject.SetActive(true); // Enables the remove button
        }
        else
        {
            removeButton.gameObject.SetActive(false); // Disables the remove button
        }

        SetRarity();

    }

    void EquipWeapon() // Equips the weapon to the character
    {
        if (weaponSelector.selectedCharacter.weaponsQuantity > 0)
        {
            //play sound effect
            ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/RejectedSelect"));
            return;
        }

        //play sound effect
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

        weaponSelector.selectedCharacter.weaponHeld = weapon; // Sets the weapon to the character
        weaponSelector.selectedCharacter.weaponsQuantity++; // Increments the weapons quantity

        GameObject iconPrefab = Resources.Load<GameObject>("Prefabs/weaponIcon"); // Load the weapon icon prefab

        weaponIcon = Instantiate(iconPrefab, canvas.transform); // Instantiate the weapon icon prefab
        weaponIcon.GetComponent<WeaponIcon>().weapon = weapon; // Sets the weapon to the weapon icon
        weaponIcon.GetComponent<WeaponIcon>().SetIcon(weaponImage.sprite, background.color, weapon); // Sets the weapon icon

        isBeingUsed = true; // Sets the weapon as being used
        gameObject.GetComponent<Button>().interactable = false; // Disables the button


         Debug.Log("Weapon Name: " + weapon.name + " is being used: " + isBeingUsed);
        Debug.Log("Selected Character: " + weaponSelector.selectedCharacter.name + " has " + weaponSelector.selectedCharacter.weaponsQuantity + " weapons");

        Debug.Log("Weapon ID: " + weapon.id); // Add this before the other logs
        Debug.Log("Weapon Tags: " + string.Join(", ", GeneralInfo.GetWeaponTags(weapon.id)));
        Debug.Log("Target Classes: " + string.Join(", ", GeneralInfo.GetWeaponsTargetClasses(weapon.id)));
        Debug.Log("Enabled Classes: " + string.Join(", ", GeneralInfo.GetWeaponsEnabledClasses(weapon.id))); 
    }

    void RemoveWeapon()
    {
        //play sound effect
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

        isBeingUsed = false; // Sets the weapon as not being used
        gameObject.GetComponent<Button>().interactable = true; // Enables the button
        weaponSelector.selectedCharacter.weaponHeld = null; // Removes the weapon from the character
        weaponSelector.selectedCharacter.weaponsQuantity--; // Decrements the weapons quantity
        //Destroy(weaponIcon); // Destroys the weapon icon

        WeaponIcon[] allIcons = FindObjectsByType<WeaponIcon>(FindObjectsSortMode.None);
        foreach (var icon in allIcons)
        {
            if (icon.weapon == weapon)
            {
                Destroy(icon.gameObject);
                break; // optional, if only one icon per weapon
            }
        }

    }

    void LevelUpWeapon()
    {
        weapon.wlBase++;
        if (weapon.wlBase > 20) return;
        weapon.SetWeapon(weapon.wlBase);
        atkw.text = weapon.atkw.ToString();
        shots.text = weapon.shots.ToString();
        lvl.text = "Lv. " + weapon.wlBase.ToString();
    }

    void SetRarity()
    {
        if (weapon.wlBase >= 1 && weapon.wlBase <= 6)
        {
            background.color = GeneralInfo.GetTierColor("TR2");
            outline.color = GeneralInfo.GetTierColor("TR2");
            weaponImage.sprite = weaponSprites[0];
            starContainer.SetActive(true);
            star1.SetActive(true);
            star2.SetActive(true);
            star3.SetActive(false);
            star4.SetActive(false);
            star5.SetActive(false);
        }
        else if (weapon.wlBase >= 7 && weapon.wlBase <= 12)
        {
            background.color = GeneralInfo.GetTierColor("TR3");
            outline.color = GeneralInfo.GetTierColor("TR3");
            weaponImage.sprite = weaponSprites[1];
            starContainer.SetActive(true);
            star1.SetActive(true);
            star2.SetActive(true);
            star3.SetActive(true);
            star4.SetActive(false);
            star5.SetActive(false);
        }
        else if (weapon.wlBase >= 13 && weapon.wlBase <= 16)
        {
            background.color = GeneralInfo.GetTierColor("TR1");
            outline.color = GeneralInfo.GetTierColor("TR1");
            weaponImage.sprite = weaponSprites[2];
            starContainer.SetActive(true);
            star1.SetActive(true);
            star2.SetActive(true);
            star3.SetActive(true);
            star4.SetActive(true);
            star5.SetActive(false);
        }
        else if (weapon.wlBase >= 17 && weapon.wlBase <= 20)
        {
            background.color = GeneralInfo.GetTierColor("TR6");
            outline.color = GeneralInfo.GetTierColor("TR6");
            weaponImage.sprite = weaponSprites[3];
            starContainer.SetActive(true);
            star1.SetActive(true);
            star2.SetActive(true);
            star3.SetActive(true);
            star4.SetActive(true);
            star5.SetActive(true);
        }
        else
        {
            background.color = Color.black;
            outline.color = Color.black;
            weaponImage.sprite = weaponSprites[4];
            starContainer.SetActive(false);
        }
    }

    public void RefreshUI()
    {
        if (weapon == null || atkw == null || shots == null || lvl == null) return;

        atkw.text = weapon.atkw.ToString();
        shots.text = weapon.shots.ToString();

        // If weapon has tg30 tag, show curse image
        if (weapon.weaponTags.Any(x => x.id == "tg30"))
        {
            curse.SetActive(true);
        }
        else
        {
            curse.SetActive(false);
        }
        lvl.text = "Lv. " + weapon.wlBase.ToString();

        // isBeingUsed is true if weapon is being held by any character
        BattleCharacter[] characters = configsHandler.GetActiveCharacters();
        isBeingUsed = characters.Any(c => c != null && c.weaponHeld == weapon);
        gameObject.GetComponent<Button>().interactable = !isBeingUsed;

        SetRarity();
    }

    public void CleanupForPooling()
    {
        weapon = null;
        atkw.text = "";
        shots.text = "";
        lvl.text = "";
        starContainer.SetActive(false);
        isBeingUsed = false;
        gameObject.GetComponent<Button>().interactable = true;
    }
}
