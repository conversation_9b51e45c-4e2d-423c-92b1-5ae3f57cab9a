using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// Singleton manager for general game information including modifiers and character rarity tiers
/// Provides global access to modifier and tier data through key-based lookup
/// Loads data from generalInfo.json and handles graceful fallbacks for missing data
///
/// Usage:
/// - GeneralInfo.GetModifierSkill("MO0") returns "Inteligência"
/// - GeneralInfo.GetTierName("TR1") returns "Épico"
/// - GeneralInfo.GetTierColor("TR1") returns "#7030a0"
///
/// The system is read-only from game code perspective - all modifications must come through import process
/// </summary>
public class GeneralInfo : MonoBehaviour
{
    #region Singleton Implementation
    private static GeneralInfo _instance;

    /// <summary>
    /// Singleton instance accessor
    /// Automatically creates instance if none exists
    /// </summary>
    public static GeneralInfo Instance
    {
        get
        {
            if (_instance == null)
            {
                // Try to find existing instance in scene
                _instance = FindFirstObjectByType<GeneralInfo>();

                if (_instance == null)
                {
                    // Create new GameObject with GeneralInfo component
                    GameObject generalInfoObject = new GameObject("GeneralInfo");
                    _instance = generalInfoObject.AddComponent<GeneralInfo>();
                    DontDestroyOnLoad(generalInfoObject);
                }
            }
            return _instance;
        }
    }
    #endregion

    #region Private Fields
    /// <summary>
    /// Dictionary for fast modifier lookup by id
    /// Key: string id (e.g., "MO0")
    /// Value: JsonModifier object containing all modifier data
    /// </summary>
    private Dictionary<string, JsonModifier> modifierLookup = new Dictionary<string, JsonModifier>();

    /// <summary>
    /// Dictionary for fast tier lookup by id
    /// Key: string id (e.g., "TR1")
    /// Value: JsonTier object containing all tier data
    /// </summary>
    private Dictionary<string, JsonTier> tierLookup = new Dictionary<string, JsonTier>();

    /// <summary>
    /// Dictionary for fast moon phase lookup by id
    /// Key: string id (e.g., "MMRANG24")
    /// Value: JsonMoonRange object containing all moon phase data
    /// </summary>
    private readonly Dictionary<string, JsonMoonRange> moonPhaseLookup = new();

    /// <summary>
    /// Dictionary for fast classes lookup by id
    /// Key: string id
    /// Value: JsonClasses object containing class data
    /// </summary>
    private Dictionary<string, JsonClasses> classesLookup = new Dictionary<string, JsonClasses>();

    /// <summary>
    /// Dictionary for fast archetypes lookup by id
    /// Key: string id
    /// Value: JsonClasses object containing archetype data
    /// </summary>
    private Dictionary<string, JsonArchetype> archetypeLookup = new Dictionary<string, JsonArchetype>();

    /// <summary>
    /// Dictionary for fast ailments lookup by id
    /// Key: string id
    /// Value: JsonClasses object containing ailment data
    /// </summary>
    private Dictionary<string, JsonAilment> ailmentLookup = new Dictionary<string, JsonAilment>();

    /// <summary>
    /// Dictionary for fast elemental defenses lookup by id
    /// Key: string id
    /// Value: JsonClasses object containing elemental defenses data
    /// </summary>
    private Dictionary<string, JsonElementalDefenses> elementalDefensesLookup = new Dictionary<string, JsonElementalDefenses>();

    /// <summary>
    /// Dictionary for fast skills lookup by id
    /// Key: string id
    /// Value: JsonClasses object containing skill data
    /// </summary>
    private Dictionary<string, JsonSkills> skillsLookup = new Dictionary<string, JsonSkills>();

    /// <summary>
    /// Dictionary for fast weapons lookup by id
    /// Key: string id
    /// Value: JsonWeapons object containing weapon data
    /// </summary>
    private Dictionary<string, JsonWeapons> weaponsLookup = new Dictionary<string, JsonWeapons>();

    /// <summary>
    /// Dictionary for fast weapon upgrades lookup by id
    /// Key: string id
    /// Value: JsonWeaponUpgrade object containing weapon upgrade data
    /// </summary>
    private Dictionary<string, JsonWeaponUpgrade> weaponUpgradeLookup = new Dictionary<string, JsonWeaponUpgrade>();

    /// <summary>
    /// Dictionary for fast tags lookup by id
    /// Key: string id
    /// Value: JsonTags object containing tag data
    /// </summary>
    private Dictionary<string, JsonTags> tagLookup = new Dictionary<string, JsonTags>();

    /// <summary>
    /// Flag to track if data has been loaded successfully
    /// </summary>
    private bool isLoaded = false;

    /// <summary>
    /// Cached reference to loaded general info data for debugging and inspection
    /// </summary>
    private JsonGeneralInfo generalInfo;
    #endregion

    #region Unity Lifecycle
    private void Awake()
    {
        // Ensure singleton pattern
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
            LoadGeneralInfo();
        }
        else if (_instance != this)
        {
            Destroy(gameObject);
        }
    }
    #endregion

    #region Modifiers Region
    /// <summary>
    /// Gets the skill name for the specified modifier id
    /// Returns the id itself if not found (graceful fallback)
    /// </summary>
    /// <param name="id">The modifier id to look up (e.g., "MO0")</param>
    /// <returns>Skill name or the id itself if not found</returns>
    public static string GetModifierSkill(string id)
    {
        return Instance.GetModifierSkillInternal(id);
    }

    /// <summary>
    /// Gets the description for the specified modifier id
    /// Returns empty string if not found
    /// </summary>
    /// <param name="id">The modifier id to look up (e.g., "MO0")</param>
    /// <returns>Description text or empty string if not found</returns>
    public static string GetModifierDescription(string id)
    {
        return Instance.GetModifierDescriptionInternal(id);
    }

    /// <summary>
    /// Gets the acronym for the specified modifier id
    /// Returns the id itself if not found (graceful fallback)
    /// </summary>
    /// <param name="id">The modifier id to look up (e.g., "MO0")</param>
    /// <returns>Acronym or the id itself if not found</returns>
    public static string GetModifierAcronym(string id)
    {
        return Instance.GetModifierAcronymInternal(id);
    }

    /// <summary>
    /// Gets the complete modifier object for advanced usage
    /// Returns null if id not found
    /// </summary>
    /// <param name="id">The modifier id to look up (e.g., "MO0")</param>
    /// <returns>JsonModifier object or null if not found</returns>
    public static JsonModifier GetModifier(string id)
    {
        return Instance.GetModifierInternal(id);
    }

    /// <summary>
    /// Gets all available modifier ids for debugging or UI population
    /// </summary>
    /// <returns>Array of all available modifier ids</returns>
    public static string[] GetAllModifierIds()
    {
        return Instance.GetAllModifierIdsInternal();
    }

    /// <summary>
    /// Gets the total number of loaded modifiers
    /// </summary>
    /// <returns>Number of modifiers currently loaded</returns>
    public static int GetModifierCount()
    {
        return Instance.modifierLookup.Count;
    }

    // Convenience methods for the 6 specific modifiers

    // Skill name methods
    /// <summary>Gets the skill name for Knowledge modifier (MO0)</summary>
    public static string GetKnowledgeSkillName() => GetModifierSkill("MO0");

    /// <summary>Gets the skill name for Luck modifier (MO1)</summary>
    public static string GetLuckSkillName() => GetModifierSkill("MO1");

    /// <summary>Gets the skill name for Speed modifier (MO2)</summary>
    public static string GetSpeedSkillName() => GetModifierSkill("MO2");

    /// <summary>Gets the skill name for Evasion modifier (MO3)</summary>
    public static string GetEvasionSkillName() => GetModifierSkill("MO3");

    /// <summary>Gets the skill name for Precision modifier (MO4)</summary>
    public static string GetPrecisionSkillName() => GetModifierSkill("MO4");

    /// <summary>Gets the skill name for Critical Chance modifier (MO5)</summary>
    public static string GetCriticalChanceSkillName() => GetModifierSkill("MO5");

    /// <summary>Gets the skill name for Parry Chance modifier (MO6)</summary>
    public static string GetParryChanceSkillName() => GetModifierSkill("MO6");


    // Acronym methods
    /// <summary>Gets the acronym for Knowledge modifier (MO0)</summary>
    public static string GetKnowledgeAcronym() => GetModifierAcronym("MO0");

    /// <summary>Gets the acronym for Luck modifier (MO1)</summary>
    public static string GetLuckAcronym() => GetModifierAcronym("MO1");

    /// <summary>Gets the acronym for Speed modifier (MO2)</summary>
    public static string GetSpeedAcronym() => GetModifierAcronym("MO2");

    /// <summary>Gets the acronym for Evasion modifier (MO3)</summary>
    public static string GetEvasionAcronym() => GetModifierAcronym("MO3");

    /// <summary>Gets the acronym for Precision modifier (MO4)</summary>
    public static string GetPrecisionAcronym() => GetModifierAcronym("MO4");

    /// <summary>Gets the acronym for Critical Chance modifier (MO5)</summary>
    public static string GetCriticalChanceAcronym() => GetModifierAcronym("MO5");

    /// <summary>Gets the acronym for Parry Chance modifier (MO6)</summary>
    public static string GetParryChanceAcronym() => GetModifierAcronym("MO6");


    // Description methods
    /// <summary>Gets the description for Knowledge modifier (MO0)</summary>
    public static string GetKnowledgeDescription() => GetModifierDescription("MO0");

    /// <summary>Gets the description for Luck modifier (MO1)</summary>
    public static string GetLuckDescription() => GetModifierDescription("MO1");

    /// <summary>Gets the description for Speed modifier (MO2)</summary>
    public static string GetSpeedDescription() => GetModifierDescription("MO2");

    /// <summary>Gets the description for Evasion modifier (MO3)</summary>
    public static string GetEvasionDescription() => GetModifierDescription("MO3");

    /// <summary>Gets the description for Precision modifier (MO4)</summary>
    public static string GetPrecisionDescription() => GetModifierDescription("MO4");

    /// <summary>Gets the description for Critical Chance modifier (MO5)</summary>
    public static string GetCriticalChanceDescription() => GetModifierDescription("MO5");

    /// <summary>Gets the description for Parry Chance modifier (MO6)</summary>
    public static string GetParryChanceDescription() => GetModifierDescription("MO6");

    #endregion

    #region Character Rarity Region
    /// <summary>
    /// Gets the display name for the specified tier id
    /// Returns the id itself if not found (graceful fallback)
    /// </summary>
    /// <param name="id">The tier id to look up (e.g., "TR1")</param>
    /// <returns>Tier name or the id itself if not found</returns>
    public static string GetTierName(string id)
    {
        return Instance.GetTierNameInternal(id);
    }

    /// <summary>
    /// Gets the variance for the specified tier id
    /// Returns empty string if not found
    /// </summary>
    /// <param name="id">The tier id to look up (e.g., "TR1")</param>
    /// <returns>Variance text or empty string if not found</returns>
    public static string GetTierVariance(string id)
    {
        return Instance.GetTierVarianceInternal(id);
    }

    /// <summary>
    /// Gets the color for the specified tier id
    /// Returns Color.white if not found or if color parsing fails (white fallback)
    /// </summary>
    /// <param name="id">The tier id to look up (e.g., "TR1")</param>
    /// <returns>Unity Color object or Color.white if not found/invalid</returns>
    public static Color GetTierColor(string id)
    {
        return Instance.GetTierColorInternal(id);
    }

    /// <summary>
    /// Gets the acronym for the specified tier id
    /// Returns the id itself if not found (graceful fallback)
    /// </summary>
    /// <param name="id">The tier id to look up (e.g., "TR1")</param>
    /// <returns>Acronym or the id itself if not found</returns>
    public static string GetTierAcronym(string id)
    {
        return Instance.GetTierAcronymInternal(id);
    }

    /// <summary>
    /// Gets the selectDrop value for the specified tier id
    /// Returns empty string if not found
    /// </summary>
    /// <param name="id">The tier id to look up (e.g., "TR1")</param>
    /// <returns>SelectDrop value or empty string if not found</returns>
    public static string GetTierSelectDrop(string id)
    {
        return Instance.GetTierSelectDropInternal(id);
    }

    /// <summary>
    /// Gets the complete tier object for advanced usage
    /// Returns null if id not found
    /// </summary>
    /// <param name="id">The tier id to look up (e.g., "TR1")</param>
    /// <returns>JsonTier object or null if not found</returns>
    public static JsonTier GetTier(string id)
    {
        return Instance.GetTierInternal(id);
    }

    /// <summary>
    /// Gets all available tier ids for debugging or UI population
    /// </summary>
    /// <returns>Array of all available tier ids</returns>
    public static string[] GetAllTierIds()
    {
        return Instance.GetAllTierIdsInternal();
    }

    /// <summary>
    /// Gets the total number of loaded tiers
    /// </summary>
    /// <returns>Number of tiers currently loaded</returns>
    public static int GetTierCount()
    {
        return Instance.tierLookup.Count;
    }
    #endregion

    #region Moon Phases Region
    /// <summary>
    /// Gets the complete moon phase object for advanced usage
    /// Returns null if id not found
    /// </summary>
    /// <param name="id">The moon phase id to look up (e.g., "MMRANG24")</param>
    /// <returns>JsonMoonRange object or null if not found</returns>
    public static JsonMoonRange GetMoonPhase(string id)
    {
        return Instance.GetMoonPhaseInternal(id);
    }

    /// <summary>
    /// Gets the display name for the specified moon phase id
    /// Returns the id itself if not found (graceful fallback)
    /// </summary>
    /// <param name="id">The moon phase id to look up (e.g., "MMRANG24")</param>
    /// <returns>Moon phase display name or the id itself if not found</returns>
    public static string GetMoonPhaseName(string id)
    {
        return Instance.GetMoonPhaseNameInternal(id);
    }

    /// <summary>
    /// Gets the technical nomenclature for the specified moon phase id
    /// Returns empty string if not found
    /// </summary>
    /// <param name="id">The moon phase id to look up (e.g., "MMRANG24")</param>
    /// <returns>Technical nomenclature or empty string if not found</returns>
    public static string GetMoonPhaseTechnicalName(string id)
    {
        return Instance.GetMoonPhaseTechnicalNameInternal(id);
    }

    /// <summary>
    /// Gets a specific knowledge value for a moon phase
    /// Returns empty string if moon phase or knowledge id not found
    /// </summary>
    /// <param name="moonPhaseId">The moon phase id to look up (e.g., "MMRANG24")</param>
    /// <param name="knowledgeId">The knowledge id to find (e.g., "KN1")</param>
    /// <returns>Knowledge value or empty string if not found</returns>
    public static string GetMoonPhaseKnowledgeValue(string moonPhaseId, string knowledgeId)
    {
        return Instance.GetMoonPhaseKnowledgeValueInternal(moonPhaseId, knowledgeId);
    }

    /// <summary>
    /// Gets a specific attribute value for a moon phase
    /// Returns empty string if moon phase or attribute id not found
    /// </summary>
    /// <param name="moonPhaseId">The moon phase id to look up (e.g., "MMRANG24")</param>
    /// <param name="attributeId">The attribute id to find (e.g., "AT1")</param>
    /// <returns>Attribute value or empty string if not found</returns>
    public static string GetMoonPhaseAttributeValue(string moonPhaseId, string attributeId)
    {
        return Instance.GetMoonPhaseAttributeValueInternal(moonPhaseId, attributeId);
    }

    /// <summary>
    /// Gets the party damage modifier for the specified moon phase id
    /// Returns empty string if not found
    /// </summary>
    /// <param name="id">The moon phase id to look up (e.g., "MMRANG24")</param>
    /// <returns>Party damage modifier or empty string if not found</returns>
    public static string GetMoonPhaseDamageParty(string id)
    {
        return Instance.GetMoonPhaseDamagePartyInternal(id);
    }

    /// <summary>
    /// Gets the opponent damage modifier for the specified moon phase id
    /// Returns empty string if not found
    /// </summary>
    /// <param name="id">The moon phase id to look up (e.g., "MMRANG24")</param>
    /// <returns>Opponent damage modifier or empty string if not found</returns>
    public static string GetMoonPhaseDamageOpponent(string id)
    {
        return Instance.GetMoonPhaseDamageOpponentInternal(id);
    }

    /// <summary>
    /// Gets all available moon phase ids for debugging or UI population
    /// </summary>
    /// <returns>Array of all available moon phase ids</returns>
    public static string[] GetAllMoonPhaseIds()
    {
        return Instance.GetAllMoonPhaseIdsInternal();
    }

    /// <summary>
    /// Gets the total number of loaded moon phases
    /// </summary>
    /// <returns>Number of moon phases currently loaded</returns>
    public static int GetMoonPhaseCount()
    {
        return Instance.moonPhaseLookup.Count;
    }
    #endregion

    #region Classes Region

    public static string GetClassName(string id)
    {
        return Instance.GetClassNameInternal(id);
    }
    public static int GetClassVoices(string id)
    {
        return Instance.GetClassVoicesInternal(id);
    }
    public static string GetClassDescription(string id)
    {
        return Instance.GetClassDescriptionInternal(id);
    }
    public static string GetClassDescriptionItem(string id)
    {
        return Instance.GetClassDescriptionItemInternal(id);
    }
    public static string GetClassNameItem(string id)
    {
        return Instance.GetClassNameItemInternal(id);
    }
    public static string GetClassNameArchetype(string id)
    {
        return Instance.GetClassNameArchetypeInternal(id);
    }

    public static string[] GetAllClassIds()
    {
        return Instance.GetAllClassIdsInternal();
    }
    public static int GetClassCount()
    {
        return Instance.classesLookup.Count;
    }
    public static JsonClasses GetClass(string id)
    {
        return Instance.GetClassInternal(id);
    }

    public static string GetClassArchetypeByName(string name)
    {
        return Instance.GetClassArchetypeByNameInternal(name);
    }

    public static string GetClassDescriptionByName(string name)
    {
        return Instance.GetClassDescriptionByNameInternal(name);
    }

    #endregion

    #region Archetypes Region

    public static string GetArchetypeName(string id)
    {
        return Instance.GetArchetypeNameInternal(id);
    }
    public static string GetArchetypeDescription(string id)
    {
        return Instance.GetArchetypeDescriptionInternal(id);
    }
    public static string GetArchetypeColor(string id)
    {
        return Instance.GetArchetypeColorInternal(id);
    }
    public static string GetArchetypeNotes(string id)
    {
        return Instance.GetArchetypeNotesInternal(id);
    }
    public static string[] GetAllArchetypeIds()
    {
        return Instance.GetAllArchetypeIdsInternal();
    }
    public static int GetArchetypeCount()
    {
        return Instance.archetypeLookup.Count;
    }
    public static JsonArchetype GetArchetype(string id)
    {
        return Instance.GetArchetypeInternal(id);
    }
    public static string GetArchetypeDescriptionByName(string name)
    {
        return Instance.GetArchetypeDescriptionByNameInternal(name);
    }

    #endregion

    #region Ailments Region

    public static string GetAilmentName(string id)
    {
        return Instance.GetAilmentNameInternal(id);
    }
    public static string GetAilmentDescription(string id)
    {
        return Instance.GetAilmentDescriptionInternal(id);
    }
    public static string[] GetAllAilmentIds()
    {
        return Instance.GetAllAilmentIdsInternal();
    }
    public static int GetAilmentCount()
    {
        return Instance.ailmentLookup.Count;
    }
    public static JsonAilment GetAilment(string id)
    {
        return Instance.GetAilmentInternal(id);
    }

    public static string GetCharmAilmentName() => GetAilmentName("AIL1");

    public static string GetCharmAilmentDescription() => GetAilmentDescription("AIL1");

    public static string GetConfusionAilmentName() => GetAilmentName("AIL0");

    public static string GetConfusionAilmentDescription() => GetAilmentDescription("AIL0");

    public static string GetCurseAilmentName() => GetAilmentName("AIL4");

    public static string GetCurseAilmentDescription() => GetAilmentDescription("AIL4");

    public static string GetParalysisAilmentName() => GetAilmentName("AIL3");

    public static string GetParalysisAilmentDescription() => GetAilmentDescription("AIL3");

    public static string GetSleepAilmentName() => GetAilmentName("AIL2");

    public static string GetSleepAilmentDescription() => GetAilmentDescription("AIL2");

    public static string GetAilmentDescriptionByName(string name)
    {
        return Instance.GetAilmentDescriptionByNameInternal(name);
    }

    #endregion

    #region Elemental Defenses Region

    public static string GetElementalDefensesName(string id)
    {
        return Instance.GetElementalDefensesNameInternal(id);
    }

    public static string GetElementalDefensesDescription(string id)
    {
        return Instance.GetElementalDefensesDescriptionInternal(id);
    }

    public static string GetElementalDefensesIdByName(string name)
    {
        return Instance.GetElementalDefensesIdByNameInternal(name);
    }
    public static string[] GetAllElementalDefensesIds()
    {
        return Instance.GetAllElementalDefensesIdsInternal();
    }
    public static int GetElementalDefensesCount()
    {
        return Instance.elementalDefensesLookup.Count;
    }
    public static JsonElementalDefenses GetElementalDefenses(string id)
    {
        return Instance.GetElementalDefensesInternal(id);
    }

    #endregion

    #region Skills Region

    public static string GetSkillsName(string id)
    {
        return Instance.GetSkillsNameInternal(id);
    }
    public static string GetSkillsAcronym(string id)
    {
        return Instance.GetSkillsAcronymInternal(id);
    }
    public static string GetSkillsDescription(string id)
    {
        return Instance.GetSkillsDescriptionInternal(id);
    }
    public static string[] GetAllSkillsIds()
    {
        return Instance.GetAllSkillsIdsInternal();
    }
    public static int GetSkillsCount()
    {
        return Instance.skillsLookup.Count;
    }
    public static JsonSkills GetSkills(string id)
    {
        return Instance.GetSkillsInternal(id);
    }

    #endregion

    #region Weapons Region

    public static Weapons[] GetAllWeapons()
    {
        return Instance.GetAllWeaponsInternal();
    }

    public static string GetWeaponsName(string id)
    {
        return Instance.GetWeaponsNameInternal(id);
    }
    public static string GetWeaponsDescription(string id)
    {
        return Instance.GetWeaponsDescriptionInternal(id);
    }

    public static string[] GetWeaponTags(string id)
    {
        return Instance.GetWeaponTagsInternal(id);
    }
    public static string[] GetAllWeaponsIds()
    {
        return Instance.GetAllWeaponsIdsInternal();
    }
    public static int GetWeaponsCount()
    {
        return Instance.weaponsLookup.Count;
    }
    public static JsonWeapons GetWeapons(string id)
    {
        return Instance.GetWeaponsInternal(id);
    }

    public static string GetWeaponsItemId(string id)
    {
        return Instance.GetWeaponItemIdInternal(id);
    }

    public static string[] GetWeaponsTargetClasses(string id)
    {
        return Instance.GetWeaponsTargetClassesInternal(id);
    }

    public static string[] GetWeaponsEnabledClasses(string id)
    {
        return Instance.GetWeaponsEnabledClassesInternal(id);
    }

    #endregion

    #region Weapon Upgrades Region

    public static WeaponUpgrade[] GetAllWeaponUpgrades()
    {
        return Instance.GetAllWeaponUpgradesInternal();
    }
    public static string GetWeaponUpgradeItemId(string id)
    {
        return Instance.GetWeaponUpgradeItemIdInternal(id);
    }

    public static int GetWeaponUpgradeLevelByItemId(string itemId)
    {
        return Instance.GetWeaponUpgradeLevelByItemIdInternal(itemId);
    }
    public static int GetWeaponUpgradeAtkWByItemId(string itemId)
    {
        return Instance.GetWeaponUpgradeAtkWByItemIdInternal(itemId);
    }

    public static int GetWeaponUpgradeCount()
    {
        return Instance.weaponUpgradeLookup.Count;
    }

    public static JsonWeaponUpgrade GetWeaponUpgradeByItemId(string itemId)
    {
        return Instance.GetWeaponUpgradeByItemIdInternal(itemId);
    }

    #endregion

    #region Tags Region

    public static string GetTagName(string id)
    {
        return Instance.GetTagNameInternal(id);
    }
    public static string GetTagNotes(string id)
    {
        return Instance.GetTagNotesInternal(id);
    }

    #endregion


    #region Public Utility Methods
    /// <summary>
    /// Forces reload of general info data from generalInfo.json
    /// Useful after import operations or for debugging
    /// </summary>
    public static void ReloadGeneralInfo()
    {
        Instance.LoadGeneralInfo();
    }

    /// <summary>
    /// Gets loading status for debugging
    /// </summary>
    /// <returns>True if data is loaded successfully</returns>
    public bool IsLoaded()
    {
        return isLoaded;
    }
    #endregion

    #region Private Implementation Methods
    /// <summary>
    /// Internal method to load modifiers and tiers from generalInfo.json
    /// Called automatically on initialization and can be called manually to reload
    /// </summary>
    private void LoadGeneralInfo()
    {
        try
        {
            // Clear existing data
            modifierLookup.Clear();
            tierLookup.Clear();
            moonPhaseLookup.Clear();
            classesLookup.Clear();
            archetypeLookup.Clear();
            ailmentLookup.Clear();
            elementalDefensesLookup.Clear();
            skillsLookup.Clear();
            weaponsLookup.Clear();
            weaponUpgradeLookup.Clear();
            tagLookup.Clear();
            isLoaded = false;

            // Load from JSON using existing JsonSaveHelper pattern
            if (JsonSaveHelper.FileExists("generalInfo.json"))
            {
                generalInfo = JsonSaveHelper.LoadFromJson<JsonGeneralInfo>("generalInfo.json");

                if (generalInfo != null)
                {
                    int modifierCount = 0;
                    int tierCount = 0;
                    int moonPhaseCount = 0;
                    int classCount = 0;
                    int archetypeCount = 0;
                    int ailmentCount = 0;
                    int elementalDefensesCount = 0;
                    int skillsCount = 0;
                    int weaponsCount = 0;
                    int weaponUpgradeCount = 0;
                    int tagCount = 0;

                    // Build modifier lookup dictionary
                    if (generalInfo.modifierListPkg != null)
                    {
                        foreach (var modifier in generalInfo.modifierListPkg)
                        {
                            if (modifier != null && !string.IsNullOrEmpty(modifier.id))
                            {
                                // Use id as dictionary key for fast lookup
                                modifierLookup[modifier.id] = modifier;
                                modifierCount++;
                            }
                        }
                    }

                    // Build tier lookup dictionary
                    if (generalInfo.tierListPkg != null)
                    {
                        foreach (var tier in generalInfo.tierListPkg)
                        {
                            if (tier != null && !string.IsNullOrEmpty(tier.id))
                            {
                                // Use id as dictionary key for fast lookup
                                tierLookup[tier.id] = tier;
                                tierCount++;
                            }
                        }
                    }

                    // Build moon phase lookup dictionary
                    if (generalInfo.modMoonRangesPkg != null)
                    {
                        foreach (var moonPhase in generalInfo.modMoonRangesPkg)
                        {
                            if (moonPhase != null && !string.IsNullOrEmpty(moonPhase.id))
                            {
                                // Use id as dictionary key for fast lookup
                                moonPhaseLookup[moonPhase.id] = moonPhase;
                                moonPhaseCount++;
                            }
                        }
                    }

                    // Build classes lookup dictionary
                    if (generalInfo.classesPkg != null)
                    {
                        foreach (var classes in generalInfo.classesPkg)
                        {
                            if (classes != null && !string.IsNullOrEmpty(classes.id))
                            {
                                // Use id as dictionary key for fast lookup
                                classesLookup[classes.id] = classes;
                                classCount++;
                            }
                        }
                    }

                    // Build archetypes lookup dictionary
                    if (generalInfo.archetypeListPkg != null)
                    {
                        foreach (var archetype in generalInfo.archetypeListPkg)
                        {
                            if (archetype != null && !string.IsNullOrEmpty(archetype.id))
                            {
                                // Use id as dictionary key for fast lookup
                                archetypeLookup[archetype.id] = archetype;
                                archetypeCount++;
                            }
                        }
                    }

                    // Build ailments lookup dictionary
                    if (generalInfo.ailmentPkg != null)
                    {
                        foreach (var ailment in generalInfo.ailmentPkg)
                        {
                            if (ailment != null && !string.IsNullOrEmpty(ailment.id))
                            {
                                // Use id as dictionary key for fast lookup
                                ailmentLookup[ailment.id] = ailment;
                                ailmentCount++;
                            }
                        }
                    }

                    // Build elemental defenses lookup dictionary
                    if (generalInfo.elementalDefensesPkg != null)
                    {
                        foreach (var elementalDefenses in generalInfo.elementalDefensesPkg)
                        {
                            if (elementalDefenses != null && !string.IsNullOrEmpty(elementalDefenses.id))
                            {
                                // Use id as dictionary key for fast lookup
                                elementalDefensesLookup[elementalDefenses.id] = elementalDefenses;
                                elementalDefensesCount++;
                            }
                        }
                    }

                    // Build skills lookup dictionary
                    if (generalInfo.statusPkg != null)
                    {
                        foreach (var status in generalInfo.statusPkg)
                        {
                            if (status != null && !string.IsNullOrEmpty(status.id))
                            {
                                // Use id as dictionary key for fast lookup
                                skillsLookup[status.id] = status;
                                skillsCount++;
                            }
                        }
                    }

                    // Build weapons lookup dictionary
                    if (generalInfo.weaponPkg != null)
                    {
                        foreach (var weapon in generalInfo.weaponPkg)
                        {
                            if (weapon != null && !string.IsNullOrEmpty(weapon.id))
                            {
                                // Use id as dictionary key for fast lookup
                                weaponsLookup[weapon.id] = weapon;
                                weaponsCount++;
                            }
                        }
                    }

                    // Build weapon upgrades lookup dictionary
                    if (generalInfo.weaponUpgradePkg != null)
                    {
                        foreach (var weaponUpgrade in generalInfo.weaponUpgradePkg)
                        {
                            if (weaponUpgrade != null && !string.IsNullOrEmpty(weaponUpgrade.id))
                            {
                                // Use id as dictionary key for fast lookup
                                weaponUpgradeLookup[weaponUpgrade.id] = weaponUpgrade;
                                weaponUpgradeCount++;
                            }
                        }
                    }

                    // Build tags lookup dictionary
                    if (generalInfo.tagPkg != null)
                    {
                        foreach (var tag in generalInfo.tagPkg)
                        {
                            if (tag != null && !string.IsNullOrEmpty(tag.id))
                            {
                                // Use id as dictionary key for fast lookup
                                tagLookup[tag.id] = tag;
                                tagCount++;
                            }
                        }
                    }

                    isLoaded = true;
                    Debug.Log($"[GeneralInfo] ✅ Loaded {modifierCount} modifiers, {tierCount} tiers, {moonPhaseCount} moon phases, {classCount} classes, {archetypeCount} archetypes, {ailmentCount} ailments, {elementalDefensesCount} elemental defenses, {skillsCount} skills, {weaponsCount} weapons, {weaponUpgradeCount} weapon upgrades and {tagCount} tags from generalInfo.json");
                }
                else
                {
                    Debug.LogWarning("[GeneralInfo] ⚠️ generalInfo.json exists but contains no data");
                }
            }
            else
            {
                Debug.LogWarning("[GeneralInfo] ⚠️ generalInfo.json not found - modifiers and tiers not available");
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"[GeneralInfo] ❌ Failed to load general info data: {ex.Message}");
            isLoaded = false;
        }
    }

    // Modifier internal implementation methods
    private string GetModifierSkillInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (modifierLookup.TryGetValue(id, out JsonModifier modifier))
        {
            return modifier.skill ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[GeneralInfo] ⚠️ Modifier id '{id}' not found, returning id as fallback");
        return id;
    }

    private string GetModifierDescriptionInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (modifierLookup.TryGetValue(id, out JsonModifier modifier))
        {
            return modifier.description ?? "";
        }

        return "";
    }

    private string GetModifierAcronymInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (modifierLookup.TryGetValue(id, out JsonModifier modifier))
        {
            return modifier.acronym ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[GeneralInfo] ⚠️ Modifier id '{id}' not found, returning id as fallback");
        return id;
    }

    private JsonModifier GetModifierInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return null;

        modifierLookup.TryGetValue(id, out JsonModifier modifier);
        return modifier;
    }

    private string[] GetAllModifierIdsInternal()
    {
        return modifierLookup.Keys.ToArray();
    }

    // Tier internal implementation methods
    private string GetTierNameInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (tierLookup.TryGetValue(id, out JsonTier tier))
        {
            return tier.name ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[GeneralInfo] ⚠️ Tier id '{id}' not found, returning id as fallback");
        return id;
    }

    private string GetTierVarianceInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (tierLookup.TryGetValue(id, out JsonTier tier))
        {
            return tier.variance ?? "";
        }

        return "";
    }

    private Color GetTierColorInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return Color.white;

        if (tierLookup.TryGetValue(id, out JsonTier tier))
        {
            string colorHex = tier.color;

            // Try to parse the hex color string
            if (!string.IsNullOrEmpty(colorHex))
            {
                if (ColorUtility.TryParseHtmlString(colorHex, out Color parsedColor))
                {
                    return parsedColor;
                }
                else
                {
                    Debug.LogWarning($"[GeneralInfo] ⚠️ Invalid color format '{colorHex}' for tier '{id}', returning white as fallback");
                    return Color.white;
                }
            }
            else
            {
                Debug.LogWarning($"[GeneralInfo] ⚠️ Empty color value for tier '{id}', returning white as fallback");
                return Color.white;
            }
        }

        // Graceful fallback - return white color
        Debug.LogWarning($"[GeneralInfo] ⚠️ Tier id '{id}' not found, returning white color as fallback");
        return Color.white;
    }

    private string GetTierAcronymInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (tierLookup.TryGetValue(id, out JsonTier tier))
        {
            return tier.acronym ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[GeneralInfo] ⚠️ Tier id '{id}' not found, returning id as fallback");
        return id;
    }

    private string GetTierSelectDropInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (tierLookup.TryGetValue(id, out JsonTier tier))
        {
            return tier.selectDrop ?? "";
        }

        return "";
    }

    private JsonTier GetTierInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return null;

        tierLookup.TryGetValue(id, out JsonTier tier);
        return tier;
    }

    private string[] GetAllTierIdsInternal()
    {
        return tierLookup.Keys.ToArray();
    }

    // Moon phase internal implementation methods
    private JsonMoonRange GetMoonPhaseInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return null;

        moonPhaseLookup.TryGetValue(id, out JsonMoonRange moonPhase);
        return moonPhase;
    }

    private string GetMoonPhaseNameInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (moonPhaseLookup.TryGetValue(id, out JsonMoonRange moonPhase))
        {
            return moonPhase.moonPhase ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[GeneralInfo] ⚠️ Moon phase id '{id}' not found, returning id as fallback");
        return id;
    }

    private string GetMoonPhaseTechnicalNameInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (moonPhaseLookup.TryGetValue(id, out JsonMoonRange moonPhase))
        {
            return moonPhase.technicalNomenclature ?? "";
        }

        return "";
    }

    private string GetMoonPhaseKnowledgeValueInternal(string moonPhaseId, string knowledgeId)
    {
        if (string.IsNullOrEmpty(moonPhaseId) || string.IsNullOrEmpty(knowledgeId))
            return "";

        if (moonPhaseLookup.TryGetValue(moonPhaseId, out JsonMoonRange moonPhase))
        {
            if (moonPhase.knowledge != null)
            {
                foreach (var knowledge in moonPhase.knowledge)
                {
                    if (knowledge != null && knowledge.id == knowledgeId)
                    {
                        return knowledge.value ?? "";
                    }
                }
            }
        }

        return "";
    }

    private string GetMoonPhaseAttributeValueInternal(string moonPhaseId, string attributeId)
    {
        if (string.IsNullOrEmpty(moonPhaseId) || string.IsNullOrEmpty(attributeId))
            return "";

        if (moonPhaseLookup.TryGetValue(moonPhaseId, out JsonMoonRange moonPhase))
        {
            if (moonPhase.attribute != null)
            {
                foreach (var attribute in moonPhase.attribute)
                {
                    if (attribute != null && attribute.id == attributeId)
                    {
                        return attribute.value ?? "";
                    }
                }
            }
        }

        return "";
    }

    private string GetMoonPhaseDamagePartyInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (moonPhaseLookup.TryGetValue(id, out JsonMoonRange moonPhase))
        {
            return moonPhase.modDanoParty ?? "";
        }

        return "";
    }

    private string GetMoonPhaseDamageOpponentInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (moonPhaseLookup.TryGetValue(id, out JsonMoonRange moonPhase))
        {
            return moonPhase.modDanoOponente ?? "";
        }

        return "";
    }

    private string[] GetAllMoonPhaseIdsInternal()
    {
        return moonPhaseLookup.Keys.ToArray();
    }

    // Classes internal implementation methods

    private string GetClassNameInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (classesLookup.TryGetValue(id, out JsonClasses classes))
        {
            return classes.name ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[GeneralInfo] ⚠️ Class id '{id}' not found, returning id as fallback");
        return id;
    }

    private int GetClassVoicesInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return 0;

        if (classesLookup.TryGetValue(id, out JsonClasses classes))
        {
            return classes.voices;
        }

        return 0;
    }

    private string GetClassDescriptionInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (classesLookup.TryGetValue(id, out JsonClasses classes))
        {
            return classes.description ?? "";
        }

        return "";
    }

    private string GetClassDescriptionItemInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (classesLookup.TryGetValue(id, out JsonClasses classes))
        {
            return classes.descriptionItem ?? "";
        }

        return "";
    }

    private string GetClassNameItemInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (classesLookup.TryGetValue(id, out JsonClasses classes))
        {
            return classes.nameItem ?? "";
        }

        return "";
    }

    private string GetClassNameArchetypeInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (classesLookup.TryGetValue(id, out JsonClasses classes))
        {
            return classes.nameArchetype ?? "";
        }

        return "";
    }
    private string GetClassArchetypeByNameInternal(string name)
    {
        if (string.IsNullOrEmpty(name))
            return "";

        foreach (var kvp in classesLookup)
        {
            if (kvp.Value.name == name)
            {
                return kvp.Value.nameArchetype;
            }
        }

        return "";
    }

    private string GetClassDescriptionByNameInternal(string name)
    {
        if (string.IsNullOrEmpty(name))
            return "";

        foreach (var kvp in classesLookup)
        {
            if (kvp.Value.name == name)
            {
                return kvp.Value.description;
            }
        }

        return "";
    }

    private string[] GetAllClassIdsInternal()
    {
        return classesLookup.Keys.ToArray();
    }

    private JsonClasses GetClassInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return null;

        classesLookup.TryGetValue(id, out JsonClasses classes);
        return classes;
    }

    // Archetype internal implementation methods
    private string GetArchetypeNameInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (archetypeLookup.TryGetValue(id, out JsonArchetype archetype))
        {
            return archetype.name ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[GeneralInfo] ⚠️ Archetype id '{id}' not found, returning id as fallback");
        return id;
    }

    private string GetArchetypeDescriptionInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (archetypeLookup.TryGetValue(id, out JsonArchetype archetype))
        {
            return archetype.description ?? "";
        }

        return "";
    }

    private string GetArchetypeColorInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (archetypeLookup.TryGetValue(id, out JsonArchetype archetype))
        {
            return archetype.color ?? "";
        }

        return "";
    }

    private string GetArchetypeNotesInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (archetypeLookup.TryGetValue(id, out JsonArchetype archetype))
        {
            return archetype.notes ?? "";
        }

        return "";
    }

    private string[] GetAllArchetypeIdsInternal()
    {
        return archetypeLookup.Keys.ToArray();
    }

    private JsonArchetype GetArchetypeInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return null;

        archetypeLookup.TryGetValue(id, out JsonArchetype archetype);
        return archetype;
    }

    private string GetArchetypeDescriptionByNameInternal(string name)
    {
        if (string.IsNullOrEmpty(name))
            return "";

        foreach (var kvp in archetypeLookup)
        {
            if (kvp.Value.name == name)
            {
                return kvp.Value.description;
            }
        }

        return "";
    }

    // Ailments internal implementation methods
    private string GetAilmentNameInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (ailmentLookup.TryGetValue(id, out JsonAilment ailment))
        {
            return ailment.ailment ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[GeneralInfo] ⚠️ Ailment id '{id}' not found, returning id as fallback");
        return id;
    }

    private string GetAilmentDescriptionInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (ailmentLookup.TryGetValue(id, out JsonAilment ailment))
        {
            return ailment.description ?? "";
        }

        return "";
    }

    private string[] GetAllAilmentIdsInternal()
    {
        return ailmentLookup.Keys.ToArray();
    }

    private JsonAilment GetAilmentInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return null;

        ailmentLookup.TryGetValue(id, out JsonAilment ailment);
        return ailment;
    }

    private string GetAilmentDescriptionByNameInternal(string name)
    {
        if (string.IsNullOrEmpty(name))
            return "";

        foreach (var kvp in ailmentLookup)
        {
            if (kvp.Value.ailment == name)
            {
                return kvp.Value.description;
            }
        }

        return "";
    }

    // Elemental Defenses internal implementation methods
    private string GetElementalDefensesNameInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (elementalDefensesLookup.TryGetValue(id, out JsonElementalDefenses elementalDefenses))
        {
            return elementalDefenses.defenses ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[GeneralInfo] ⚠️ Elemental Defenses id '{id}' not found, returning id as fallback");
        return id;
    }

    private string GetElementalDefensesIdByNameInternal(string name)
    {
        if (string.IsNullOrEmpty(name))
            return "";

        foreach (var kvp in elementalDefensesLookup)
        {
            if (kvp.Value.defenses == name)
            {
                return kvp.Key;
            }
        }

        return "";
    }

    private string GetElementalDefensesDescriptionInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (elementalDefensesLookup.TryGetValue(id, out JsonElementalDefenses elementalDefenses))
        {
            return elementalDefenses.description ?? "";
        }

        return "";
    }

    private string[] GetAllElementalDefensesIdsInternal()
    {
        return elementalDefensesLookup.Keys.ToArray();
    }

    private JsonElementalDefenses GetElementalDefensesInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return null;

        elementalDefensesLookup.TryGetValue(id, out JsonElementalDefenses elementalDefenses);
        return elementalDefenses;
    }

    // Skills internal implementation methods
    private string GetSkillsNameInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (skillsLookup.TryGetValue(id, out JsonSkills skills))
        {
            return skills.skills ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[GeneralInfo] ⚠️ Skills id '{id}' not found, returning id as fallback");
        return id;
    }

    private string GetSkillsAcronymInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (skillsLookup.TryGetValue(id, out JsonSkills skills))
        {
            return skills.acronym ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[GeneralInfo] ⚠️ Skills id '{id}' not found, returning id as fallback");
        return id;
    }

    private string GetSkillsDescriptionInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (skillsLookup.TryGetValue(id, out JsonSkills skills))
        {
            return skills.description ?? "";
        }

        return "";
    }

    private string[] GetAllSkillsIdsInternal()
    {
        return skillsLookup.Keys.ToArray();
    }

    private JsonSkills GetSkillsInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return null;

        skillsLookup.TryGetValue(id, out JsonSkills skills);
        return skills;
    }

    // Weapons internal implementation methods

    private Weapons[] GetAllWeaponsInternal()
    {
        WeaponUpgrade[] weaponUpgrades = GetAllWeaponUpgradesInternal();

        Weapons[] weapons = weaponsLookup.Values.Select(x => new Weapons(
            x.id,
            x.itemId,
            x.name,
            x.description,
            x.wlBase,   // Base WL
            x.qiMin,   // Min Qi
            x.luckMin,   // Min Luck
            x.hc,   // HC
            x.isSpecial,   // Is special
            x.weaponTags.Select(wt => new WeaponTags(wt)).ToArray(),   // Weapon tags
            x.targetClasses.Select(y => new TargetClasses(y)).ToArray(),   // Target classes
            x.enabledClasses.Select(z => new EnabledClasses(z)).ToArray(),   // Enabled classes
            weaponUpgrades.Where(wu => wu.itemId == x.itemId).ToArray()   // Weapon upgrades
        )).ToArray();

        return weapons;

    }
    private string GetWeaponsNameInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (weaponsLookup.TryGetValue(id, out JsonWeapons weapons))
        {
            return weapons.name ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[GeneralInfo] ⚠️ Weapons id '{id}' not found, returning id as fallback");
        return id;
    }
    private string GetWeaponsDescriptionInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (weaponsLookup.TryGetValue(id, out JsonWeapons weapons))
        {
            return weapons.description ?? "";
        }

        return "";
    }

    private string[] GetWeaponTagsInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return new string[0];

        if (weaponsLookup.TryGetValue(id, out JsonWeapons weapons))
        {
            return weapons.weaponTags?.ToArray() ?? new string[0];
        }

        return new string[0];
    }

    private string[] GetAllWeaponsIdsInternal()
    {
        return weaponsLookup.Keys.ToArray();
    }

    private JsonWeapons GetWeaponsInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return null;

        weaponsLookup.TryGetValue(id, out JsonWeapons weapons);
        return weapons;
    }
    private string GetWeaponItemIdInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (weaponsLookup.TryGetValue(id, out JsonWeapons weapons))
        {
            return weapons.itemId ?? "";
        }

        return "";
    }
    private string[] GetWeaponsTargetClassesInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return new string[0];

        if (weaponsLookup.TryGetValue(id, out JsonWeapons weapons))
        {
            return weapons.targetClasses?.ToArray() ?? new string[0];
        }

        return new string[0];
    }
    private string[] GetWeaponsEnabledClassesInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return new string[0];

        if (weaponsLookup.TryGetValue(id, out JsonWeapons weapons))
        {
            return weapons.enabledClasses?.ToArray() ?? new string[0];
        }

        return new string[0];
    }

    // Weapon Upgrades internal implementation methods

    private WeaponUpgrade[] GetAllWeaponUpgradesInternal()
    {
        return weaponUpgradeLookup.Values.Select(x => new WeaponUpgrade(
            x.id,
            x.itemId,
            x.level,
            x.atkw,
            x.cooldown,
            x.gold,
            x.ichor,
            x.souls,
            x.time,
            x.rubies,
            x.titanium,
            x.adamantium,
            x.rarityName,
            x.shots,
            x.spdBoost,
            x.goldUp,
            x.hellnium
        )).ToArray();
    }
    private string GetWeaponUpgradeItemIdInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (weaponUpgradeLookup.TryGetValue(id, out JsonWeaponUpgrade weaponUpgrade))
        {
            return weaponUpgrade.itemId ?? "";
        }

        return "";
    }
    private int GetWeaponUpgradeLevelByItemIdInternal(string itemId)
    {
        if (string.IsNullOrEmpty(itemId))
            return 0;

        foreach (var kvp in weaponUpgradeLookup)
        {
            if (kvp.Value.itemId == itemId)
            {
                return kvp.Value.level;
            }
        }

        return 0;
    }
    private int GetWeaponUpgradeAtkWByItemIdInternal(string itemId)
    {
        if (string.IsNullOrEmpty(itemId))
            return 0;

        foreach (var kvp in weaponUpgradeLookup)
        {
            if (kvp.Value.itemId == itemId)
            {
                return kvp.Value.atkw;
            }
        }

        return 0;
    }
    private JsonWeaponUpgrade GetWeaponUpgradeByItemIdInternal(string itemId)
    {
        if (string.IsNullOrEmpty(itemId))
            return null;

        foreach (var kvp in weaponUpgradeLookup)
        {
            if (kvp.Value.itemId == itemId)
            {
                return kvp.Value;
            }
        }

        return null;
    }

    // Tags internal implementation methods
    private string GetTagNameInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (tagLookup.TryGetValue(id, out JsonTags tag))
        {
            return tag.name ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[GeneralInfo] ⚠️ Tag id '{id}' not found, returning id as fallback");
        return id;
    }

    private string GetTagNotesInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (tagLookup.TryGetValue(id, out JsonTags tag))
        {
            return tag.notes ?? "";
        }

        return "";
    }

    #endregion

    #region Debug and Utility Methods
    /// <summary>
    /// Debug method to print all loaded modifiers to console
    /// Useful for development and troubleshooting
    /// </summary>
    [ContextMenu("Debug Print All Modifiers")]
    public void DebugPrintAllModifiers()
    {
        if (!isLoaded)
        {
            Debug.Log("[GeneralInfo] 🔍 No data loaded");
            return;
        }

        Debug.Log($"[GeneralInfo] 🔍 Loaded Modifiers ({modifierLookup.Count} total):");
        foreach (var kvp in modifierLookup)
        {
            var modifier = kvp.Value;
            Debug.Log($"  ID: '{modifier.id}' | Skill: '{modifier.skill}' | Acronym: '{modifier.acronym}' | Description: '{modifier.description}'");
        }
    }

    /// <summary>
    /// Debug method to print all loaded tiers to console
    /// Useful for development and troubleshooting
    /// </summary>
    [ContextMenu("Debug Print All Tiers")]
    public void DebugPrintAllTiers()
    {
        if (!isLoaded)
        {
            Debug.Log("[GeneralInfo] 🔍 No data loaded");
            return;
        }

        Debug.Log($"[GeneralInfo] 🔍 Loaded Tiers ({tierLookup.Count} total):");
        foreach (var kvp in tierLookup)
        {
            var tier = kvp.Value;
            Color parsedColor = GetTierColorInternal(tier.id);
            Debug.Log($"  ID: '{tier.id}' | Name: '{tier.name}' | Acronym: '{tier.acronym}' | Color: '{tier.color}' (Parsed: {parsedColor}) | Variance: '{tier.variance}' | SelectDrop: '{tier.selectDrop}'");
        }
    }

    /// <summary>
    /// Debug method to print all loaded moon phases to console
    /// Useful for development and troubleshooting
    /// </summary>
    [ContextMenu("Debug Print All Moon Phases")]
    public void DebugPrintAllMoonPhases()
    {
        if (!isLoaded)
        {
            Debug.Log("[GeneralInfo] 🔍 No data loaded");
            return;
        }

        Debug.Log($"[GeneralInfo] 🔍 Loaded Moon Phases ({moonPhaseLookup.Count} total):");
        foreach (var kvp in moonPhaseLookup)
        {
            var moonPhase = kvp.Value;
            Debug.Log($"  ID: '{moonPhase.id}' | Phase: '{moonPhase.moonPhase}' | Technical: '{moonPhase.technicalNomenclature}'");
            Debug.Log($"    Party Damage: '{moonPhase.modDanoParty}' | Opponent Damage: '{moonPhase.modDanoOponente}'");

            if (moonPhase.knowledge != null && moonPhase.knowledge.Length > 0)
            {
                var knowledgeValues = moonPhase.knowledge.Select(k => $"{k.id}:{k.value}").ToArray();
                Debug.Log($"    Knowledge: [{string.Join(", ", knowledgeValues)}]");
            }

            if (moonPhase.attribute != null && moonPhase.attribute.Length > 0)
            {
                var attributeValues = moonPhase.attribute.Select(a => $"{a.id}:{a.value}").ToArray();
                Debug.Log($"    Attributes: [{string.Join(", ", attributeValues)}]");
            }
        }
    }

    [ContextMenu("Debug Print All Classes")]
    public void DebugPrintAllClasses()
    {
        if (!isLoaded)
        {
            Debug.Log("[GeneralInfo] 🔍 No data loaded");
            return;
        }

        Debug.Log($"[GeneralInfo] 🔍 Loaded Classes ({classesLookup.Count} total):");

        foreach (var kvp in classesLookup)
        {
            var classes = kvp.Value;
            Debug.Log($"  ID: '{classes.id}' | Name: '{classes.name}' | Voices: '{classes.voices}' | Description: '{classes.description}' | Description Item: '{classes.descriptionItem}' | Name Item: '{classes.nameItem}' | Name Archetype: '{classes.nameArchetype}'");
        }
    }

    [ContextMenu("Debug Print All Archetypes")]
    public void DebugPrintAllArchetypes()
    {
        if (!isLoaded)
        {
            Debug.Log("[GeneralInfo] 🔍 No data loaded");
            return;
        }

        Debug.Log($"[GeneralInfo] 🔍 Loaded Archetypes ({archetypeLookup.Count} total):");
        foreach (var kvp in archetypeLookup)
        {
            var archetype = kvp.Value;
            Debug.Log($"  ID: '{archetype.id}' | Name: '{archetype.name}' | Description: '{archetype.description}' | Color: '{archetype.color}' | Notes: '{archetype.notes}'");
        }
    }

    [ContextMenu("Debug Print All Ailments")]
    public void DebugPrintAllAilments()
    {
        if (!isLoaded)
        {
            Debug.Log("[GeneralInfo] 🔍 No data loaded");
            return;
        }

        Debug.Log($"[GeneralInfo] 🔍 Loaded Ailments ({ailmentLookup.Count} total):");
        foreach (var kvp in ailmentLookup)
        {
            var ailment = kvp.Value;
            Debug.Log($"  ID: '{ailment.id}' | Ailment: '{ailment.ailment}' | Description: '{ailment.description}'");
        }
    }

    [ContextMenu("Debug Print All Elemental Defenses")]
    public void DebugPrintAllElementalDefenses()
    {
        if (!isLoaded)
        {
            Debug.Log("[GeneralInfo] 🔍 No data loaded");
            return;
        }

        Debug.Log($"[GeneralInfo] 🔍 Loaded Elemental Defenses ({elementalDefensesLookup.Count} total):");
        foreach (var kvp in elementalDefensesLookup)
        {
            var elementalDefenses = kvp.Value;
            Debug.Log($"  ID: '{elementalDefenses.id}' | Defenses: '{elementalDefenses.defenses}' | Description: '{elementalDefenses.description}'");
        }
    }

    [ContextMenu("Debug Print All Skills")]
    public void DebugPrintAllSkills()
    {
        if (!isLoaded)
        {
            Debug.Log("[GeneralInfo] 🔍 No data loaded");
            return;
        }

        Debug.Log($"[GeneralInfo] 🔍 Loaded Skills ({skillsLookup.Count} total):");
        foreach (var kvp in skillsLookup)
        {
            var skills = kvp.Value;
            Debug.Log($"  ID: '{skills.id}' | Skills: '{skills.skills}' | Acronym: '{skills.acronym}' | Description: '{skills.description}'");
        }
    }

    [ContextMenu("Debug Print All Weapons")]
    public void DebugPrintAllWeapons()
    {
        if (!isLoaded)
        {
            Debug.Log("[GeneralInfo] 🔍 No data loaded");
            return;
        }

        Debug.Log($"[GeneralInfo] 🔍 Loaded Weapons ({weaponsLookup.Count} total):");
        foreach (var kvp in weaponsLookup)
        {
            var weapons = kvp.Value;
            Debug.Log($"  ID: '{weapons.id}' | Name: '{weapons.name}' | ItemId: '{weapons.itemId}' | Description: '{weapons.description}' | WLBase: '{weapons.wlBase}' | QIMin: '{weapons.qiMin}' | LuckMin: '{weapons.luckMin}' | HC: '{weapons.hc}' | IsSpecial: '{weapons.isSpecial}'");
        }
    }

    [ContextMenu("Debug Print All Weapon Upgrades")]
    public void DebugPrintAllWeaponUpgrades()
    {
        if (!isLoaded)
        {
            Debug.Log("[GeneralInfo] 🔍 No data loaded");
            return;
        }

        Debug.Log($"[GeneralInfo] 🔍 Loaded Weapon Upgrades ({weaponUpgradeLookup.Count} total):");
        foreach (var kvp in weaponUpgradeLookup)
        {
            var weaponUpgrade = kvp.Value;
            Debug.Log($"  ID: '{weaponUpgrade.id}' | ItemId: '{weaponUpgrade.itemId}' | Level: '{weaponUpgrade.level}' | AtkW: '{weaponUpgrade.atkw}' | Cooldown: '{weaponUpgrade.cooldown}' | Gold: '{weaponUpgrade.gold}' | Ichor: '{weaponUpgrade.ichor}' | Souls: '{weaponUpgrade.souls}' | Time: '{weaponUpgrade.time}' | Rubies: '{weaponUpgrade.rubies}' | Titanium: '{weaponUpgrade.titanium}' | Adamantium: '{weaponUpgrade.adamantium}' | RarityName: '{weaponUpgrade.rarityName}' | Shots: '{weaponUpgrade.shots}' | SpeedBoost: '{weaponUpgrade.spdBoost}' | GoldUpgrade: '{weaponUpgrade.goldUp}' | Hellnium: '{weaponUpgrade.hellnium}'");
        }
    }

    [ContextMenu("Debug Print All Tags")]
    public void DebugPrintAllTags()
    {
        if (!isLoaded)
        {
            Debug.Log("[GeneralInfo] 🔍 No data loaded");
            return;
        }

        Debug.Log($"[GeneralInfo] 🔍 Loaded Tags ({tagLookup.Count} total):");
        foreach (var kvp in tagLookup)
        {
            var tag = kvp.Value;
            Debug.Log($"  ID: '{tag.id}' | Name: '{tag.name}' | Notes: '{tag.notes}'");
        }
    }

    /// <summary>
    /// Debug method to print all loaded data (modifiers, tiers, and moon phases)
    /// </summary>
    [ContextMenu("Debug Print All General Info")]
    public void DebugPrintAllGeneralInfo()
    {
        DebugPrintAllModifiers();
        DebugPrintAllTiers();
        DebugPrintAllMoonPhases();
        DebugPrintAllClasses();
        DebugPrintAllArchetypes();
        DebugPrintAllAilments();
        DebugPrintAllElementalDefenses();
        DebugPrintAllSkills();
        DebugPrintAllWeapons();
        DebugPrintAllWeaponUpgrades();
        DebugPrintAllTags();
    }
    #endregion

}
