using UnityEngine;
using UnityEngine.UI;

public class CombatInfo : MonoBehaviour
{

    public GameObject PlayerTurn, EnemyTurn, Turn, Attacks, Combo, FPSCounter, moonRnfBtn, partydmg, enemydmg;

    public Camera mainCam;
    public Camera configCam;
    public Canvas GamePlayCanvas;
    public Canvas ConfigsCanvas;


    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        GetComponent<Button>().onClick.AddListener(Hide);
    }

    private void Hide()
    {
        // play the select sound effect
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

        if (PlayerTurn.activeSelf) PlayerTurn.SetActive(false); else PlayerTurn.SetActive(true);
        if (EnemyTurn.activeSelf) EnemyTurn.SetActive(false); else EnemyTurn.SetActive(true);
        if (Turn.activeSelf) Turn.SetActive(false); else Turn.SetActive(true);
        if (Attacks.activeSelf) Attacks.SetActive(false); else Attacks.SetActive(true);
        if (Combo.activeSelf) Combo.SetActive(false); else Combo.SetActive(true);
        if (FPSCounter.activeSelf) FPSCounter.SetActive(false); else FPSCounter.SetActive(true);
        if (moonRnfBtn.activeSelf) moonRnfBtn.SetActive(false); else moonRnfBtn.SetActive(true);
        if (partydmg.activeSelf) partydmg.SetActive(false); else partydmg.SetActive(true);
        if (enemydmg.activeSelf) enemydmg.SetActive(false); else enemydmg.SetActive(true);
    }
}
